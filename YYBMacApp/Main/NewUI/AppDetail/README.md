# App详情页架构设计

## 概述

基于 `YYBHomePageView` 的架构设计，实现了统一的App详情页框架，支持三种不同类型的详情页，并且都能自动响应导航栏的展开/收起状态变化。

## 架构设计

### 基类：BaseAppDetailView

`BaseAppDetailView` 是所有详情页的基类，采用 `NSCollectionView` 架构，提供了以下核心功能：

1. **导航栏状态监听**：自动监听 `kYYBMainNavigationViewExpandedDidChangeNotification` 通知
2. **边距自动调整**：根据导航栏展开/收起状态自动计算和调整内容边距
3. **动画支持**：提供平滑的动画过渡效果
4. **CollectionView架构**：采用与 `YYBHomePageView` 相同的布局模式

#### 布局结构

```
NSCollectionView
├── Section 0 (Header): topViewComponent
└── Section 1 (Items): 内容组件数组
```

#### 核心属性

- `topViewHeight`：头部视图高度
- `contentEdgeInsets`：内容边距，会根据导航栏状态自动调整
- `isNavigationViewExpanded`：导航栏展开状态
- `detailCollectionView`：详情页CollectionView
- `scrollView`：详情页ScrollView

#### 核心方法

- `setEdgeInsets:animated:`：设置内容边距，支持动画
- `leftPadding` / `rightPadding`：根据导航栏状态计算边距
- `createTopView`：子类重写创建头部视图
- `getContentComponents`：子类重写返回内容组件数组
- `createContentItemWithComponent:atIndexPath:`：子类重写创建内容项
- `sizeForContentItemWithComponent:atIndexPath:`：子类重写返回内容项大小

### 三种详情页类型

#### 1. TopViewAppDetailView
- **用途**：适用于需要突出展示的重要应用
- **特点**：包含头部视图和AppOtherInfoView组件
- **头部高度**：400pt
- **内容组件**：`@[@"AppOtherInfo"]`

#### 2. GameAppDetailView
- **用途**：专门为游戏应用设计
- **特点**：包含游戏截图、游戏描述等游戏特有元素
- **头部高度**：350pt
- **特色**：深色主题，适合游戏展示
- **内容组件**：`@[@"GameDescription"]`

#### 3. AppDetailView（原StandardAppDetailView）
- **用途**：适用于普通应用
- **特点**：简洁的布局，包含App图标、名称、开发者信息
- **头部高度**：250pt
- **特色**：清爽的浅色主题
- **内容组件**：`@[]`（空数组，后续可扩展）

## 导航栏响应机制

### 工作原理

1. **通知监听**：所有详情页都会在初始化时自动监听导航栏状态变化通知
2. **边距计算**：根据导航栏展开状态（80pt vs 144pt）自动计算左右边距
3. **布局更新**：通过 `invalidateLayout` 和 `layoutSubtreeIfNeeded` 重新布局CollectionView
4. **动画过渡**：使用 `NSAnimationContext` 提供平滑的动画效果

### 边距计算逻辑

```objc
- (NSEdgeInsets)collectionView:(NSCollectionView *)collectionView 
                        layout:(NSCollectionViewLayout *)collectionViewLayout 
        insetForSectionAtIndex:(NSInteger)section {
    // 详情页相当于topViewComponent永为true
    if (section == 0) {
        return NSEdgeInsetsMake(0, 0, 0, 0);
    } else if (section == 1) {
        return NSEdgeInsetsMake(-100, self.contentEdgeInsets.left, kTopBottomPadding, self.contentEdgeInsets.right);
    }
    
    return NSEdgeInsetsMake(kTopBottomPadding, self.contentEdgeInsets.left, kTopBottomPadding, self.contentEdgeInsets.right);
}
```

## 使用方法

### 基本使用

```objc
// 创建TopView类型详情页
YYBAppItem *appItem = [[YYBAppItem alloc] init];
appItem.name = @"我的应用";
TopViewAppDetailView *detailView = [[TopViewAppDetailView alloc] initWithAppItem:appItem];

// 添加到父视图
[parentView addSubview:detailView];
[detailView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.edges.equalTo(parentView);
}];
```

### 自定义详情页

如果需要创建新的详情页类型，只需：

1. 继承 `BaseAppDetailView`
2. 重写 `createTopView`、`getContentComponents`、`createContentItemWithComponent:atIndexPath:`、`sizeForContentItemWithComponent:atIndexPath:` 方法
3. 可选择重写 `setupDefaultValues` 来设置不同的默认值

```objc
@interface CustomAppDetailView : BaseAppDetailView
@end

@implementation CustomAppDetailView

- (NSView *)createTopView {
    // 创建自定义头部视图
    return customHeaderView;
}

- (NSArray *)getContentComponents {
    // 返回自定义内容组件数组
    return @[@"CustomComponent1", @"CustomComponent2"];
}

- (NSCollectionViewItem *)createContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    // 创建自定义内容项
    return customItem;
}

- (NSSize)sizeForContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    // 返回自定义内容项大小
    return customSize;
}

@end
```

## 文件结构

```
YYBMacApp/Main/NewUI/AppDetail/
├── BaseAppDetailView.h/.m             # 基类
├── TopViewAppDetailView.h/.m          # TopView类型详情页
├── GameAppDetailView.h/.m             # 游戏类型详情页  
├── AppDetailView.h/.m                 # 标准类型详情页（原StandardAppDetailView）
└── README.md                          # 详细文档
```

## 优势

1. **统一性**：所有详情页都有一致的导航栏响应行为
2. **可扩展性**：通过继承基类可以轻松创建新的详情页类型
3. **自动化**：无需手动处理导航栏状态变化，基类自动处理
4. **动画效果**：提供平滑的过渡动画
5. **代码复用**：公共逻辑在基类中实现，避免重复代码
6. **架构一致性**：与 `YYBHomePageView` 采用相同的布局架构

## 注意事项

1. 子类必须重写 `createTopView`、`getContentComponents`、`createContentItemWithComponent:atIndexPath:`、`sizeForContentItemWithComponent:atIndexPath:` 方法
2. 内容组件可以是任意类型的对象，通过字符串标识或更复杂的数据结构都可以
3. 所有约束都应该考虑 `self.contentEdgeInsets` 的值
4. 记得在 `dealloc` 中移除通知监听（基类已处理）
5. 详情页相当于 `topViewComponent` 永为 true 的状态
