//
//  TopViewAppDetailView.m
//  YYBMacApp
//
//  Created by l<PERSON><PERSON><PERSON> on 23/08/2025.
//

#import "TopViewAppDetailView.h"
#import "appOtherInfoView.h"
#import "MainUIDefine.h"
#import "Masonry.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

// 布局常量
static const CGFloat kPadding = 20.0;                   // 内边距

static NSString *const kTag = @"TopViewAppDetailView";

@interface TopViewAppDetailView ()

@property (nonatomic, strong) NSView *topHeaderView;            // 头部视图
@property (nonatomic, strong) AppOtherInfoView *otherInfoView;  // 其他信息视图
@property (nonatomic, strong) NSArray *contentComponents;       // 内容组件数组

@end

@implementation TopViewAppDetailView

#pragma mark - 重写基类方法

- (void)setupDefaultValues {
    [super setupDefaultValues];
    self.topViewHeight = 400; // TopView类型使用更高的头部

    // 初始化内容组件（目前只有一个AppOtherInfoView组件）
    self.contentComponents = @[@"AppOtherInfo"]; // 使用字符串标识，后续可以扩展为更复杂的数据结构
}

- (NSView *)createTopView {
    YYBMacLogInfo(kTag, @"创建TopView类型详情页头部视图");

    // 创建头部视图
    self.topHeaderView = [[NSView alloc] init];
    self.topHeaderView.wantsLayer = YES;
    self.topHeaderView.layer.backgroundColor = [[NSColor colorWithRed:0.1 green:0.1 blue:0.1 alpha:0.8] CGColor];

    // TODO: 在这里添加头部的具体内容，比如App图标、名称、评分等
    // 后续会在这里加入topViewComponent

    return self.topHeaderView;
}

- (NSArray *)getContentComponents {
    return self.contentComponents;
}

- (NSCollectionViewItem *)createContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    YYBMacLogInfo(kTag, @"创建TopView类型详情页内容项: %@", component);

    NSCollectionViewItem *item = [self.detailCollectionView makeItemWithIdentifier:@"BaseDetailItem" forIndexPath:indexPath];

    if ([component isEqualToString:@"AppOtherInfo"]) {
        // 创建AppOtherInfoView
        if (!self.otherInfoView) {
            self.otherInfoView = [[AppOtherInfoView alloc] init];
        }

        // 将AppOtherInfoView添加到item的view中
        [item.view addSubview:self.otherInfoView];
        [self.otherInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(item.view);
        }];
    }

    return item;
}

- (NSSize)sizeForContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = self.frame.size.width - [self leftPadding] - [self rightPadding];

    if ([component isEqualToString:@"AppOtherInfo"]) {
        return NSMakeSize(width, 220); // AppOtherInfoView的固定高度
    }

    return NSMakeSize(width, 100); // 默认高度
}

- (void)updateWithAppItem:(YYBAppItem *)appItem {
    [super updateWithAppItem:appItem];

    YYBMacLogInfo(kTag, @"更新TopView类型详情页App数据: %@", appItem.name);

    // 更新其他信息
    NSDictionary *appInfo = @{
        @"version": appItem ? (appItem.versionName ?: @"1.0.0") : @"1.2.3",
        @"size": appItem ? @([appItem.size longLongValue] > 0 ? [appItem.size longLongValue] : 52428800) : @(52428800),
        @"updateTime": appItem ? (appItem.updateTime ?: @"2025-08-23") : @"2025-08-23",
        @"developer": appItem ? (appItem.developer ?: @"未知开发者") : @"Apple Inc.",
        @"category": appItem ? (appItem.cateName ?: @"游戏") : @"游戏",
        @"language": @[@"中文", @"英文"]
    };

    YYBMacLogInfo(kTag, @"更新App信息: %@", appInfo);

    // 更新其他信息视图
    if (self.otherInfoView) {
        [self.otherInfoView updateWithAppInfo:appInfo];
    }
}

@end

