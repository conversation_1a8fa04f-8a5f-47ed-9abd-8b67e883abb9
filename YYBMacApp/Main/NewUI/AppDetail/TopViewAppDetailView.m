//
//  TopViewAppDetailView.m
//  YYBMacApp
//
//  Created by l<PERSON><PERSON><PERSON> on 23/08/2025.
//

#import "TopViewAppDetailView.h"
#import "appOtherInfoView.h"
#import "MainUIDefine.h"
#import "Masonry.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

// 布局常量
static const CGFloat kPadding = 20.0;                   // 内边距

static NSString *const kTag = @"TopViewAppDetailView";

@interface TopViewAppDetailView ()

@property (nonatomic, strong) NSView *headerView;               // 头部视图
@property (nonatomic, strong) AppOtherInfoView *otherInfoView;  // 其他信息视图

@end

@implementation TopViewAppDetailView

#pragma mark - 重写基类方法

- (void)setupHeaderView {
    YYBMacLogInfo(kTag, @"设置TopView类型详情页头部视图");

    // 创建头部视图
    self.headerView = [[NSView alloc] init];
    self.headerView.wantsLayer = YES;
    self.headerView.layer.backgroundColor = [[NSColor colorWithRed:0.1 green:0.1 blue:0.1 alpha:0.8] CGColor];
    [self addSubview:self.headerView];

    // TODO: 在这里添加头部的具体内容，比如App图标、名称、评分等
}

- (void)setupContentView {
    YYBMacLogInfo(kTag, @"设置TopView类型详情页内容视图");

    // 创建其他信息视图
    self.otherInfoView = [[AppOtherInfoView alloc] init];
    YYBMacLogInfo(kTag, @"创建AppOtherInfoView: %@", self.otherInfoView);
    [self addSubview:self.otherInfoView];
}

- (void)updateConstraints {
    [super updateConstraints];

//    YYBMacLogInfo(kTag, @"更新TopView类型详情页约束，contentEdgeInsets: %@", NSStringFromEdgeInsets(self.contentEdgeInsets));

    // 头部视图约束
    [self.headerView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self);
        make.left.equalTo(self).offset(self.contentEdgeInsets.left);
        make.right.equalTo(self).offset(-self.contentEdgeInsets.right);
        make.height.mas_equalTo(self.topViewHeight);
    }];

    // 内容视图约束（其他信息）
    [self.otherInfoView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.headerView.mas_bottom).offset(kPadding);
        make.left.equalTo(self).offset(self.contentEdgeInsets.left + kPadding);
        make.right.equalTo(self).offset(-self.contentEdgeInsets.right - kPadding);
        make.height.mas_equalTo(220);
    }];
}

- (void)updateWithAppItem:(YYBAppItem *)appItem {
    [super updateWithAppItem:appItem];

    YYBMacLogInfo(kTag, @"更新TopView类型详情页App数据: %@", appItem.name);

    // 更新其他信息
    NSDictionary *appInfo = @{
        @"version": appItem ? (appItem.versionName ?: @"1.0.0") : @"1.2.3",
        @"size": appItem ? @([appItem.size longLongValue] > 0 ? [appItem.size longLongValue] : 52428800) : @(52428800),
        @"updateTime": appItem ? (appItem.updateTime ?: @"2025-08-23") : @"2025-08-23",
        @"developer": appItem ? (appItem.developer ?: @"未知开发者") : @"Apple Inc.",
        @"category": appItem ? (appItem.cateName ?: @"游戏") : @"游戏",
        @"language": @[@"中文", @"英文"]
    };

    YYBMacLogInfo(kTag, @"更新App信息: %@", appInfo);

    // 更新其他信息视图
    if (self.otherInfoView) {
        [self.otherInfoView updateWithAppInfo:appInfo];
    }
}

@end

