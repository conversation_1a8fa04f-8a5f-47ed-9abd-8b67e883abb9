//
//  TopViewAppDetailView.m
//  YYBMacApp
//
//  Created by l<PERSON><PERSON><PERSON> on 23/08/2025.
//

#import "TopViewAppDetailView.h"
#import "appOtherInfoView.h"
#import "MainUIDefine.h"
#import "Masonry.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>



// 布局常量
static const CGFloat kPadding = 20.0;                   // 内边距

static NSString *const kTag = @"TopViewAppDetailView";

@interface TopViewAppDetailView ()

@property (nonatomic, strong) YYBAppItem *appItem;              // App数据

@property (nonatomic, strong) AppOtherInfoView *otherInfoView;  // 其他信息视图

@end

@implementation TopViewAppDetailView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        YYBMacLogInfo(kTag, @"初始化详情页视图，frame: %@", NSStringFromRect(frameRect));
        [self setupViews];
        [self setupConstraints];
    }
    return self;
}

- (instancetype)initWithAppItem:(YYBAppItem *)appItem {
    self = [self initWithFrame:NSZeroRect];
    if (self) {
        self.appItem = appItem;
        [self updateWithAppItem:appItem];
    }
    return self;
}

- (void)setupViews {
    YYBMacLogInfo(kTag, @"开始设置详情页视图");
    self.wantsLayer = YES;
    self.layer.backgroundColor = [[NSColor clearColor] CGColor]; // 透明背景

    // 其他信息视图
    [self setupOtherInfoView];

    YYBMacLogInfo(kTag, @"详情页视图设置完成");
}

- (void)setupOtherInfoView {
    self.otherInfoView = [[AppOtherInfoView alloc] init];
    YYBMacLogInfo(kTag, @"创建AppOtherInfoView: %@", self.otherInfoView);
    [self addSubview:self.otherInfoView];
}

- (void)setupConstraints {

    // 其他信息
    [self.otherInfoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(kPadding);
        make.right.equalTo(self.mas_centerX).offset(kNavigationDefaultWidth);
        make.left.equalTo(self).offset(-kPadding);
        make.height.mas_equalTo(220);
    }];
}

- (void)updateWithAppItem:(YYBAppItem *)appItem {
    YYBMacLogInfo(kTag, @"更新App数据: %@", appItem.name);

    // 更新其他信息
    NSDictionary *appInfo = @{
        @"version": appItem ? (appItem.versionName ?: @"1.0.0") : @"1.2.3",
        @"size": appItem ? @([appItem.size longLongValue] > 0 ? [appItem.size longLongValue] : 52428800) : @(52428800),
        @"updateTime": appItem ? (appItem.updateTime ?: @"2025-08-23") : @"2025-08-23",
        @"developer": appItem ? (appItem.developer ?: @"未知开发者") : @"Apple Inc.",
        @"category": appItem ? (appItem.cateName ?: @"游戏") : @"游戏",
        @"language": @[@"中文", @"英文"]
    };

    YYBMacLogInfo(kTag, @"更新App信息: %@", appInfo);

    // 更新其他信息视图
    [self.otherInfoView updateWithAppInfo:appInfo];
}

@end

