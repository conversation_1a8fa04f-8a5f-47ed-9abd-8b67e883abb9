//
//  AppDetailView.m
//  YYBMacApp
//
//  Created by l<PERSON><PERSON><PERSON> on 23/08/2025.
//

#import "BaseAppDetailView.h"
#import "MainUIDefine.h"
#import "Masonry.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <QuartzCore/QuartzCore.h>
#import "OverlapHeaderFlowLayout.h"

// 布局常量
static const CGFloat kLeftRightPadding = 64; // 未展开状态下的左右边距
static const CGFloat kTopBottomPadding = 24; // 顶部和底部的边距

static NSString *const kTag = @"AppDetailView";

@interface BaseAppDetailView () <NSCollectionViewDataSource, NSCollectionViewDelegate, NSCollectionViewDelegateFlowLayout>

@property (nonatomic, strong) YYBAppItem *appItem;              // App数据
@property (nonatomic, strong) NSCollectionView *detailCollectionView;
@property (nonatomic, strong) NSScrollView *scrollView;
@property (nonatomic, strong) NSView *topView;                  // 头部视图缓存

@end

@implementation BaseAppDetailView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        YYBMacLogInfo(kTag, @"初始化详情页基类视图，frame: %@", NSStringFromRect(frameRect));
        [self setupDefaultValues];
        [self setupViews];
        [self setupNotifications];
    }
    return self;
}

- (instancetype)initWithAppItem:(YYBAppItem *)appItem {
    self = [self initWithFrame:NSZeroRect];
    if (self) {
        self.appItem = appItem;
        [self updateWithAppItem:appItem];
    }
    return self;
}

- (void)dealloc {
    [NSNotificationCenter.defaultCenter removeObserver:self];
}

- (void)setupDefaultValues {
    self.topViewHeight = 300; // 默认头部高度

    // 检查当前导航栏的展开状态
    [self updateNavigationExpandedState];

    self.contentEdgeInsets = NSEdgeInsetsMake(kTopBottomPadding, [self leftPadding], kTopBottomPadding, [self rightPadding]);
}

- (void)updateNavigationExpandedState {
    // 从MMKV读取导航栏的展开状态
    self.isNavigationViewExpanded = [[YYBMacMMKV sharedInstance] getBoolForKey:@"YYBMainNavigationViewExpanded" defaultValue:NO];

    YYBMacLogInfo(kTag, @"从MMKV获取导航栏展开状态: %@", self.isNavigationViewExpanded ? @"展开" : @"收起");
}

- (void)setupViews {
    YYBMacLogInfo(kTag, @"开始设置详情页基类视图");
    self.wantsLayer = YES;
    self.layer.backgroundColor = [[NSColor clearColor] CGColor]; // 透明背景

    [self setupCollectionView];

    YYBMacLogInfo(kTag, @"详情页基类视图设置完成");
}

- (void)setupCollectionView {
    // 创建布局（使用OverlapHeaderFlowLayout以支持header重叠效果）
    NSCollectionViewFlowLayout *layout = [[OverlapHeaderFlowLayout alloc] init];
    layout.scrollDirection = NSCollectionViewScrollDirectionVertical;
    layout.minimumInteritemSpacing = 0;
    layout.minimumLineSpacing = 16;
    layout.itemSize = NSMakeSize(100, 100); // 防止初始size为0导致crash

    // 创建CollectionView
    NSCollectionView *collectionView = [[NSCollectionView alloc] initWithFrame:NSZeroRect];
    collectionView.collectionViewLayout = layout;
    collectionView.dataSource = self;
    collectionView.delegate = self;
    collectionView.backgroundColors = @[[NSColor clearColor]];
    collectionView.wantsLayer = YES;

    // 注册基础的cell类型
    [collectionView registerClass:[NSCollectionViewItem class] forItemWithIdentifier:@"BaseDetailItem"];
    // 注册header视图
    [collectionView registerClass:[NSView class] forSupplementaryViewOfKind:NSCollectionElementKindSectionHeader withIdentifier:@"DetailTopView"];

    self.detailCollectionView = collectionView;

    // 创建ScrollView
    NSScrollView *scrollView = [[NSScrollView alloc] initWithFrame:self.bounds];
    scrollView.documentView = collectionView;
    scrollView.hasVerticalScroller = YES;
    scrollView.backgroundColor = [NSColor clearColor];
    self.scrollView = scrollView;
    [self addSubview:scrollView];

    [scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    YYBMacLogInfo(kTag, @"详情页CollectionView设置完成");
}

- (void)setupNotifications {
    [NSNotificationCenter.defaultCenter addObserver:self
                                             selector:@selector(navigationViewExpandedDidChange:)
                                                 name:kYYBMainNavigationViewExpandedDidChangeNotification
                                               object:nil];
}

- (void)setEdgeInsets:(NSEdgeInsets)edgeInsets animated:(BOOL)animated {
    if (animated) {
        [NSAnimationContext runAnimationGroup:^(NSAnimationContext * _Nonnull context) {
            context.duration = kExpandAnimationDuration;
            context.allowsImplicitAnimation = YES;
            context.timingFunction = [CAMediaTimingFunction functionWithName:kExpandAnimationTimingFunction];
            self.contentEdgeInsets = edgeInsets;
            [self.detailCollectionView.collectionViewLayout invalidateLayout];
            [self.detailCollectionView layoutSubtreeIfNeeded];
        } completionHandler:nil];
    } else {
        self.contentEdgeInsets = edgeInsets;
        [self.detailCollectionView reloadData];
    }
}

- (CGFloat)leftPadding {
    BOOL isExpanded = self.isNavigationViewExpanded;
    if (isExpanded) {
        return [self rightPadding] + kNavigationExpandWidth;
    } else {
        return [self rightPadding] + kNavigationDefaultWidth;
    }
}

- (CGFloat)rightPadding {
    BOOL isExpanded = self.isNavigationViewExpanded;
    if (isExpanded) {
        return kLeftRightPadding - (kNavigationExpandWidth - kNavigationDefaultWidth) / 2;
    } else {
        return kLeftRightPadding;
    }
}

#pragma mark - Navigation Expanded
- (void)navigationViewExpandedDidChange:(NSNotification *)notification {
    BOOL expanded = [notification.userInfo[@"expanded"] boolValue];
    self.isNavigationViewExpanded = expanded;
    [self setEdgeInsets:NSEdgeInsetsMake(kTopBottomPadding, [self leftPadding], kTopBottomPadding, [self rightPadding]) animated:YES];
}

#pragma mark - NSCollectionViewDataSource

- (NSInteger)numberOfSectionsInCollectionView:(NSCollectionView *)collectionView {
    // 详情页固定有topViewComponent，所以总是2个section
    return 2;
}

- (NSInteger)collectionView:(NSCollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    if (section == 0) {
        return 0; // section 0 只有header，没有items
    }

    NSArray *contentComponents = [self getContentComponents];
    return contentComponents.count;
}

- (NSCollectionViewItem *)collectionView:(NSCollectionView *)collectionView itemForRepresentedObjectAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *contentComponents = [self getContentComponents];
    id component = contentComponents[indexPath.item];

    return [self createContentItemWithComponent:component atIndexPath:indexPath];
}

#pragma mark - NSCollectionViewDelegateFlowLayout

- (NSSize)collectionView:(NSCollectionView *)collectionView
                  layout:(NSCollectionViewLayout *)collectionViewLayout
referenceSizeForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return NSMakeSize(collectionView.frame.size.width, self.topViewHeight);
    } else {
        return NSMakeSize(0, 0);
    }
}

- (NSView *)collectionView:(NSCollectionView *)collectionView
viewForSupplementaryElementOfKind:(NSCollectionViewSupplementaryElementKind)kind
               atIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0 && kind == NSCollectionElementKindSectionHeader) {
        // 必须通过makeSupplementaryViewOfKind方法获取视图
        NSView *headerView = [collectionView makeSupplementaryViewOfKind:kind
                                                          withIdentifier:@"DetailTopView"
                                                            forIndexPath:indexPath];

        // 如果还没有设置内容，则创建并设置
        if (headerView.subviews.count == 0) {
            NSView *topView = [self createTopView];
            [headerView addSubview:topView];
            [topView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.edges.equalTo(headerView);
            }];
            self.topView = topView; // 保存引用
        }

        return headerView;
    }

    return nil;
}

- (NSSize)collectionView:(NSCollectionView *)collectionView layout:(NSCollectionViewLayout *)layout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *contentComponents = [self getContentComponents];
    id component = contentComponents[indexPath.item];

    return [self sizeForContentItemWithComponent:component atIndexPath:indexPath];
}

- (NSEdgeInsets)collectionView:(NSCollectionView *)collectionView layout:(NSCollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    // 详情页相当于topViewComponent永为true
    if (section == 0) {
        return NSEdgeInsetsMake(0, 0, 0, 0);
    } else if (section == 1) {
        return NSEdgeInsetsMake(-100, self.contentEdgeInsets.left, kTopBottomPadding, self.contentEdgeInsets.right);
    }

    return NSEdgeInsetsMake(kTopBottomPadding, self.contentEdgeInsets.left, kTopBottomPadding, self.contentEdgeInsets.right);
}

#pragma mark - 子类需要重写的方法

- (NSView *)createTopView {
    // 子类重写此方法来创建头部视图
    YYBMacLogInfo(kTag, @"基类createTopView - 子类应该重写此方法");
    NSView *defaultTopView = [[NSView alloc] init];
    defaultTopView.wantsLayer = YES;
    defaultTopView.layer.backgroundColor = [[NSColor lightGrayColor] CGColor];
    return defaultTopView;
}

- (NSArray *)getContentComponents {
    // 子类重写此方法来返回内容组件
    YYBMacLogInfo(kTag, @"基类getContentComponents - 子类应该重写此方法");
    return @[];
}

- (NSCollectionViewItem *)createContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    // 子类重写此方法来创建内容项
    YYBMacLogInfo(kTag, @"基类createContentItemWithComponent - 子类应该重写此方法");
    NSCollectionViewItem *item = [self.detailCollectionView makeItemWithIdentifier:@"BaseDetailItem" forIndexPath:indexPath];
    return item;
}

- (NSSize)sizeForContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    // 子类重写此方法来返回内容项大小
    YYBMacLogInfo(kTag, @"基类sizeForContentItemWithComponent - 子类应该重写此方法");
    CGFloat width = self.frame.size.width - [self leftPadding] - [self rightPadding];
    return NSMakeSize(width, 100);
}

- (void)updateWithAppItem:(YYBAppItem *)appItem {
    // 子类重写此方法来更新App数据
    YYBMacLogInfo(kTag, @"基类updateWithAppItem - 子类应该重写此方法");
    self.appItem = appItem;
    [self.detailCollectionView reloadData];
}

@end

