//
//  AppDetailView.m
//  YYBMacApp
//
//  Created by l<PERSON><PERSON><PERSON> on 23/08/2025.
//

#import "AppDetailView.h"
#import "MainUIDefine.h"
#import "Masonry.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <QuartzCore/QuartzCore.h>

// 布局常量
static const CGFloat kLeftRightPadding = 64; // 未展开状态下的左右边距
static const CGFloat kTopBottomPadding = 24; // 顶部和底部的边距

static NSString *const kTag = @"AppDetailView";

@interface AppDetailView ()

@property (nonatomic, strong) YYBAppItem *appItem;              // App数据

@end

@implementation AppDetailView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        YYBMacLogInfo(kTag, @"初始化详情页基类视图，frame: %@", NSStringFromRect(frameRect));
        [self setupDefaultValues];
        [self setupViews];
        [self setupNotifications];
    }
    return self;
}

- (instancetype)initWithAppItem:(YYBAppItem *)appItem {
    self = [self initWithFrame:NSZeroRect];
    if (self) {
        self.appItem = appItem;
        [self updateWithAppItem:appItem];
    }
    return self;
}

- (void)dealloc {
    [NSNotificationCenter.defaultCenter removeObserver:self];
}

- (void)setupDefaultValues {
    self.topViewHeight = 300; // 默认头部高度
    self.contentEdgeInsets = NSEdgeInsetsMake(kTopBottomPadding, [self leftPadding], kTopBottomPadding, [self rightPadding]);
}

- (void)setupViews {
    YYBMacLogInfo(kTag, @"开始设置详情页基类视图");
    self.wantsLayer = YES;
    self.layer.backgroundColor = [[NSColor clearColor] CGColor]; // 透明背景

    // 调用子类方法设置头部和内容视图
    [self setupHeaderView];
    [self setupContentView];
    [self updateConstraints];

    YYBMacLogInfo(kTag, @"详情页基类视图设置完成");
}

- (void)setupNotifications {
    [NSNotificationCenter.defaultCenter addObserver:self
                                             selector:@selector(navigationViewExpandedDidChange:)
                                                 name:kYYBMainNavigationViewExpandedDidChangeNotification
                                               object:nil];
}

- (void)setEdgeInsets:(NSEdgeInsets)edgeInsets animated:(BOOL)animated {
    if (animated) {
        [NSAnimationContext runAnimationGroup:^(NSAnimationContext * _Nonnull context) {
            context.duration = kExpandAnimationDuration;
            context.allowsImplicitAnimation = YES;
            context.timingFunction = [CAMediaTimingFunction functionWithName:kExpandAnimationTimingFunction];
            self.contentEdgeInsets = edgeInsets;
            [self updateConstraints];
            [self layoutSubtreeIfNeeded];
        } completionHandler:nil];
    } else {
        self.contentEdgeInsets = edgeInsets;
        [self updateConstraints];
    }
}

- (CGFloat)leftPadding {
    BOOL isExpanded = self.isNavigationViewExpanded;
    if (isExpanded) {
        return [self rightPadding] + kNavigationExpandWidth;
    } else {
        return [self rightPadding] + kNavigationDefaultWidth;
    }
}

- (CGFloat)rightPadding {
    BOOL isExpanded = self.isNavigationViewExpanded;
    if (isExpanded) {
        return kLeftRightPadding - (kNavigationExpandWidth - kNavigationDefaultWidth) / 2;
    } else {
        return kLeftRightPadding;
    }
}

#pragma mark - Navigation Expanded
- (void)navigationViewExpandedDidChange:(NSNotification *)notification {
    BOOL expanded = [notification.userInfo[@"expanded"] boolValue];
    self.isNavigationViewExpanded = expanded;
    [self setEdgeInsets:NSEdgeInsetsMake(kTopBottomPadding, [self leftPadding], kTopBottomPadding, [self rightPadding]) animated:YES];
}

#pragma mark - 子类需要重写的方法

- (void)setupHeaderView {
    // 子类重写此方法来设置头部视图
    YYBMacLogInfo(kTag, @"基类setupHeaderView - 子类应该重写此方法");
}

- (void)setupContentView {
    // 子类重写此方法来设置内容视图
    YYBMacLogInfo(kTag, @"基类setupContentView - 子类应该重写此方法");
}

- (void)updateConstraints {
    [super updateConstraints];
    // 子类重写此方法来更新约束
    YYBMacLogInfo(kTag, @"基类updateConstraints - 子类应该重写此方法");
}

- (void)updateWithAppItem:(YYBAppItem *)appItem {
    // 子类重写此方法来更新App数据
    YYBMacLogInfo(kTag, @"基类updateWithAppItem - 子类应该重写此方法");
    self.appItem = appItem;
}

@end

