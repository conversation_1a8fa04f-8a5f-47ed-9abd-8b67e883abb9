//
//  AppDetailView.m
//  YYBMacApp
//
//  Created by liche<PERSON><PERSON> on 23/08/2025.
//

#import "AppDetailView.h"
#import "MainUIDefine.h"
#import "Masonry.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

// 布局常量
static const CGFloat kStandardPadding = 20.0;           // 标准页面内边距
static const CGFloat kStandardHeaderHeight = 250.0;    // 标准头部高度

static NSString *const kTag = @"AppDetailView";

@interface AppDetailView ()

@property (nonatomic, strong) NSView *standardHeaderView;       // 标准头部视图
@property (nonatomic, strong) NSImageView *appIconView;        // App图标视图
@property (nonatomic, strong) NSTextField *appNameLabel;       // App名称标签
@property (nonatomic, strong) NSTextField *appInfoLabel;       // App信息标签
@property (nonatomic, strong) NSArray *contentComponents;      // 内容组件数组

@end

@implementation AppDetailView

#pragma mark - 重写基类方法

- (void)setupDefaultValues {
    [super setupDefaultValues];
    self.topViewHeight = kStandardHeaderHeight; // 标准页面头部高度
    
    // 初始化内容组件（标准详情页暂时没有内容组件）
    self.contentComponents = @[]; // 空数组，后续可以添加更多内容组件
}

- (NSView *)createTopView {
    YYBMacLogInfo(kTag, @"创建标准类型详情页头部视图");
    
    // 创建标准头部视图
    self.standardHeaderView = [[NSView alloc] init];
    self.standardHeaderView.wantsLayer = YES;
    self.standardHeaderView.layer.backgroundColor = [[NSColor colorWithRed:0.95 green:0.95 blue:0.95 alpha:1.0] CGColor];
    
    // App图标视图
    self.appIconView = [[NSImageView alloc] init];
    self.appIconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    self.appIconView.imageAlignment = NSImageAlignCenter;
    self.appIconView.wantsLayer = YES;
    self.appIconView.layer.cornerRadius = 12.0;
    self.appIconView.layer.backgroundColor = [[NSColor lightGrayColor] CGColor];
    [self.standardHeaderView addSubview:self.appIconView];
    
    // App名称标签
    self.appNameLabel = [[NSTextField alloc] init];
    self.appNameLabel.stringValue = @"应用名称";
    self.appNameLabel.font = [NSFont systemFontOfSize:24 weight:NSFontWeightBold];
    self.appNameLabel.textColor = [NSColor labelColor];
    [self.appNameLabel setBezeled:NO];
    [self.appNameLabel setDrawsBackground:NO];
    [self.appNameLabel setEditable:NO];
    [self.appNameLabel setSelectable:NO];
    self.appNameLabel.alignment = NSTextAlignmentLeft;
    [self.standardHeaderView addSubview:self.appNameLabel];
    
    // App信息标签
    self.appInfoLabel = [[NSTextField alloc] init];
    self.appInfoLabel.stringValue = @"开发者 • 版本信息";
    self.appInfoLabel.font = [NSFont systemFontOfSize:14 weight:NSFontWeightRegular];
    self.appInfoLabel.textColor = [NSColor secondaryLabelColor];
    [self.appInfoLabel setBezeled:NO];
    [self.appInfoLabel setDrawsBackground:NO];
    [self.appInfoLabel setEditable:NO];
    [self.appInfoLabel setSelectable:NO];
    self.appInfoLabel.alignment = NSTextAlignmentLeft;
    [self.standardHeaderView addSubview:self.appInfoLabel];
    
    // 设置约束
    [self setupHeaderConstraints];
    
    return self.standardHeaderView;
}

- (void)setupHeaderConstraints {
    // App图标视图约束
    [self.appIconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.standardHeaderView).offset(kStandardPadding);
        make.centerY.equalTo(self.standardHeaderView);
        make.width.height.mas_equalTo(80);
    }];
    
    // App名称标签约束
    [self.appNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.appIconView.mas_right).offset(kStandardPadding);
        make.right.equalTo(self.standardHeaderView).offset(-kStandardPadding);
        make.top.equalTo(self.appIconView).offset(10);
        make.height.mas_equalTo(30);
    }];
    
    // App信息标签约束
    [self.appInfoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.appNameLabel);
        make.top.equalTo(self.appNameLabel.mas_bottom).offset(8);
        make.height.mas_equalTo(20);
    }];
}

- (NSArray *)getContentComponents {
    return self.contentComponents;
}

- (NSCollectionViewItem *)createContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    YYBMacLogInfo(kTag, @"创建标准类型详情页内容项: %@", component);
    
    NSCollectionViewItem *item = [self.detailCollectionView makeItemWithIdentifier:@"BaseDetailItem" forIndexPath:indexPath];
    
    // 标准详情页暂时没有内容组件，这里可以根据需要添加
    
    return item;
}

- (NSSize)sizeForContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = self.frame.size.width - [self leftPadding] - [self rightPadding];
    return NSMakeSize(width, 100); // 默认高度
}

- (void)updateWithAppItem:(YYBAppItem *)appItem {
    [super updateWithAppItem:appItem];
    
    YYBMacLogInfo(kTag, @"更新标准类型详情页App数据: %@", appItem.name);
    
    // 更新App名称
    self.appNameLabel.stringValue = appItem.name ?: @"未知应用";
    
    // 更新App信息
    NSString *developer = appItem.developer ?: @"未知开发者";
    NSString *version = appItem.versionName ?: @"1.0.0";
    self.appInfoLabel.stringValue = [NSString stringWithFormat:@"%@ • %@", developer, version];
    
    // TODO: 加载App图标
    // TODO: 更新其他应用信息
}

@end
