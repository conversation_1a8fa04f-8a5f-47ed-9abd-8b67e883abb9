//
//  AppDetailView.h
//  YYBMacApp
//
//  Created by l<PERSON><PERSON><PERSON> on 23/08/2025.
//

#import <Cocoa/Cocoa.h>

@class YYBAppItem;

NS_ASSUME_NONNULL_BEGIN

/**
 * App详情页基类
 * 提供导航栏展开/收起的统一处理逻辑
 */
@interface AppDetailView : NSView

/**
 * 头部视图高度，子类可以重写
 */
@property (nonatomic, assign) CGFloat topViewHeight;

/**
 * 内容边距，会根据导航栏状态自动调整
 */
@property (nonatomic, assign) NSEdgeInsets contentEdgeInsets;

/**
 * 导航栏是否展开
 */
@property (nonatomic, assign) BOOL isNavigationViewExpanded;

/**
 * 使用App数据初始化详情页
 * @param appItem App数据模型
 */
- (instancetype)initWithAppItem:(YYBAppItem *)appItem;

/**
 * 更新App数据
 * @param appItem App数据模型
 */
- (void)updateWithAppItem:(YYBAppItem *)appItem;

/**
 * 设置内容边距
 * @param edgeInsets 内边距
 * @param animated 是否使用动画
 */
- (void)setEdgeInsets:(NSEdgeInsets)edgeInsets animated:(BOOL)animated;

/**
 * 计算左边距（根据导航栏状态）
 */
- (CGFloat)leftPadding;

/**
 * 计算右边距（根据导航栏状态）
 */
- (CGFloat)rightPadding;

/**
 * 子类可以重写的方法：设置默认值
 */
- (void)setupDefaultValues;

/**
 * 子类需要重写的方法：设置头部视图
 */
- (void)setupHeaderView;

/**
 * 子类需要重写的方法：设置内容视图
 */
- (void)setupContentView;

/**
 * 子类需要重写的方法：更新约束
 */
- (void)updateConstraints;

@end

NS_ASSUME_NONNULL_END
