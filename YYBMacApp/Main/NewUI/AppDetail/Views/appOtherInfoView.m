//
//  appOtherInfoView.m
//  YYBMacApp
//
//  Created by lichen<PERSON> on 23/08/2025.
//

#import "appOtherInfoView.h"
#import "Masonry.h"

// 布局常量
static const CGFloat kPadding = 20.0;                   // 内边距
//static const CGFloat kItemSpacing = 12.0;               // 信息项之间的间距
static const CGFloat kCornerRadius = 8.0;               // 圆角半径

@interface AppOtherInfoView ()

@property (nonatomic, strong) NSView *containerView;                // 容器视图
@property (nonatomic, strong) NSTextField *versionLabel;            // 版本标签
@property (nonatomic, strong) NSTextField *versionValueLabel;       // 版本值标签
@property (nonatomic, strong) NSTextField *sizeLabel;               // 大小标签
@property (nonatomic, strong) NSTextField *sizeValueLabel;          // 大小值标签
@property (nonatomic, strong) NSTextField *updateTimeLabel;         // 更新时间标签
@property (nonatomic, strong) NSTextField *updateTimeValueLabel;    // 更新时间值标签
@property (nonatomic, strong) NSTextField *developerLabel;          // 开发者标签
@property (nonatomic, strong) NSTextField *developerValueLabel;     // 开发者值标签
@property (nonatomic, strong) NSTextField *categoryLabel;           // 分类标签
@property (nonatomic, strong) NSTextField *categoryValueLabel;      // 分类值标签
@property (nonatomic, strong) NSTextField *languageLabel;           // 语言标签
@property (nonatomic, strong) NSTextField *languageValueLabel;      // 语言值标签

@end

@implementation AppOtherInfoView

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupViews];
        [self setupConstraints];
    }
    return self;
}

- (void)setupViews {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor colorWithWhite:0.1 alpha:1.0].CGColor; // 深灰色背景匹配黑色主题
    self.layer.cornerRadius = kCornerRadius;

    // 创建容器视图
    self.containerView = [[NSView alloc] init];
    [self addSubview:self.containerView];

    // 创建所有标签
    [self createLabels];
}

- (void)createLabels {
    // 版本
    self.versionLabel = [self createTitleLabel:@"版本"];
    self.versionValueLabel = [self createValueLabel:@"--"];

    // 大小
    self.sizeLabel = [self createTitleLabel:@"大小"];
    self.sizeValueLabel = [self createValueLabel:@"--"];

    // 更新时间
    self.updateTimeLabel = [self createTitleLabel:@"更新时间"];
    self.updateTimeValueLabel = [self createValueLabel:@"--"];

    // 开发者
    self.developerLabel = [self createTitleLabel:@"开发者"];
    self.developerValueLabel = [self createValueLabel:@"--"];

    // 分类
    self.categoryLabel = [self createTitleLabel:@"分类"];
    self.categoryValueLabel = [self createValueLabel:@"--"];

    // 语言
    self.languageLabel = [self createTitleLabel:@"语言"];
    self.languageValueLabel = [self createValueLabel:@"--"];

    // 添加到容器视图
    [self.containerView addSubview:self.versionLabel];
    [self.containerView addSubview:self.versionValueLabel];
    [self.containerView addSubview:self.sizeLabel];
    [self.containerView addSubview:self.sizeValueLabel];
    [self.containerView addSubview:self.updateTimeLabel];
    [self.containerView addSubview:self.updateTimeValueLabel];
    [self.containerView addSubview:self.developerLabel];
    [self.containerView addSubview:self.developerValueLabel];
    [self.containerView addSubview:self.categoryLabel];
    [self.containerView addSubview:self.categoryValueLabel];
    [self.containerView addSubview:self.languageLabel];
    [self.containerView addSubview:self.languageValueLabel];
}

- (NSTextField *)createTitleLabel:(NSString *)title {
    NSTextField *label = [[NSTextField alloc] init];
    label.editable = NO;
    label.bordered = NO;
    label.backgroundColor = [NSColor clearColor];
    label.font = [NSFont systemFontOfSize:14 weight:NSFontWeightMedium];
    label.textColor = [NSColor whiteColor]; // 白色文字适应深色背景
    label.stringValue = title;
    return label;
}

- (NSTextField *)createValueLabel:(NSString *)value {
    NSTextField *label = [[NSTextField alloc] init];
    label.editable = NO;
    label.bordered = NO;
    label.backgroundColor = [NSColor clearColor];
    label.font = [NSFont systemFontOfSize:14 weight:NSFontWeightRegular];
    label.textColor = [NSColor lightGrayColor]; // 浅灰色文字适应深色背景
    label.alignment = NSTextAlignmentRight;
    label.stringValue = value;
    return label;
}

- (void)setupConstraints {
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self).insets(NSEdgeInsetsMake(kPadding, kPadding, kPadding, kPadding));
    }];

    CGFloat currentY = 0;

    // 版本
    [self.versionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView);
        make.top.equalTo(self.containerView).offset(currentY);
    }];
    [self.versionValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.containerView);
        make.centerY.equalTo(self.versionLabel);
    }];
    currentY += 30;

    // 大小
    [self.sizeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView);
        make.top.equalTo(self.containerView).offset(currentY);
    }];
    [self.sizeValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.containerView);
        make.centerY.equalTo(self.sizeLabel);
    }];
    currentY += 30;

    // 更新时间
    [self.updateTimeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView);
        make.top.equalTo(self.containerView).offset(currentY);
    }];
    [self.updateTimeValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.containerView);
        make.centerY.equalTo(self.updateTimeLabel);
    }];
    currentY += 30;

    // 开发者
    [self.developerLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView);
        make.top.equalTo(self.containerView).offset(currentY);
    }];
    [self.developerValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.containerView);
        make.centerY.equalTo(self.developerLabel);
    }];
    currentY += 30;

    // 分类
    [self.categoryLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView);
        make.top.equalTo(self.containerView).offset(currentY);
    }];
    [self.categoryValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.containerView);
        make.centerY.equalTo(self.categoryLabel);
    }];
    currentY += 30;

    // 语言
    [self.languageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.containerView);
        make.top.equalTo(self.containerView).offset(currentY);
    }];
    [self.languageValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.containerView);
        make.centerY.equalTo(self.languageLabel);
    }];
}

#pragma mark - 公共方法

- (void)updateWithAppInfo:(NSDictionary *)appInfo {
    if (!appInfo) {
        return;
    }

    // 更新版本
    self.versionValueLabel.stringValue = [self formatVersion:appInfo[@"version"]];

    // 更新大小
    self.sizeValueLabel.stringValue = [self formatFileSize:appInfo[@"size"]];

    // 更新时间
    self.updateTimeValueLabel.stringValue = [self formatUpdateTime:appInfo[@"updateTime"]];

    // 更新开发者
    self.developerValueLabel.stringValue = [self formatDeveloper:appInfo[@"developer"]];

    // 更新分类
    self.categoryValueLabel.stringValue = [self formatCategory:appInfo[@"category"]];

    // 更新语言
    self.languageValueLabel.stringValue = [self formatLanguage:appInfo[@"language"]];
}

#pragma mark - 格式化方法

- (NSString *)formatVersion:(id)version {
    if ([version isKindOfClass:[NSString class]] && [(NSString *)version length] > 0) {
        return version;
    }
    return @"--";
}

- (NSString *)formatFileSize:(id)size {
    if ([size isKindOfClass:[NSNumber class]]) {
        NSNumber *sizeNumber = (NSNumber *)size;
        long long bytes = [sizeNumber longLongValue];

        if (bytes < 1024) {
            return [NSString stringWithFormat:@"%lld B", bytes];
        } else if (bytes < 1024 * 1024) {
            return [NSString stringWithFormat:@"%.1f KB", bytes / 1024.0];
        } else if (bytes < 1024 * 1024 * 1024) {
            return [NSString stringWithFormat:@"%.1f MB", bytes / (1024.0 * 1024.0)];
        } else {
            return [NSString stringWithFormat:@"%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0)];
        }
    }
    return @"--";
}

- (NSString *)formatUpdateTime:(id)updateTime {
    if ([updateTime isKindOfClass:[NSString class]] && [(NSString *)updateTime length] > 0) {
        return updateTime;
    } else if ([updateTime isKindOfClass:[NSDate class]]) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateStyle = NSDateFormatterMediumStyle;
        formatter.timeStyle = NSDateFormatterNoStyle;
        return [formatter stringFromDate:(NSDate *)updateTime];
    }
    return @"--";
}

- (NSString *)formatDeveloper:(id)developer {
    if ([developer isKindOfClass:[NSString class]] && [(NSString *)developer length] > 0) {
        return developer;
    }
    return @"--";
}

- (NSString *)formatCategory:(id)category {
    if ([category isKindOfClass:[NSString class]] && [(NSString *)category length] > 0) {
        return category;
    }
    return @"--";
}

- (NSString *)formatLanguage:(id)language {
    if ([language isKindOfClass:[NSString class]] && [(NSString *)language length] > 0) {
        return language;
    } else if ([language isKindOfClass:[NSArray class]]) {
        NSArray *languages = (NSArray *)language;
        if (languages.count > 0) {
            return [languages componentsJoinedByString:@", "];
        }
    }
    return @"--";
}

@end

