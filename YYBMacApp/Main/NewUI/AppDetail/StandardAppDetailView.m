//
//  StandardAppDetailView.m
//  YYBMacApp
//
//  Created by l<PERSON><PERSON><PERSON> on 23/08/2025.
//

#import "StandardAppDetailView.h"
#import "MainUIDefine.h"
#import "Masonry.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

// 布局常量
static const CGFloat kStandardPadding = 20.0;           // 标准页面内边距
static const CGFloat kStandardHeaderHeight = 250.0;    // 标准头部高度

static NSString *const kTag = @"StandardAppDetailView";

@interface StandardAppDetailView ()

@property (nonatomic, strong) NSView *standardHeaderView;       // 标准头部视图
@property (nonatomic, strong) NSView *standardContentView;     // 标准内容视图
@property (nonatomic, strong) NSImageView *appIconView;        // App图标视图
@property (nonatomic, strong) NSTextField *appNameLabel;       // App名称标签
@property (nonatomic, strong) NSTextField *appInfoLabel;       // App信息标签

@end

@implementation StandardAppDetailView

#pragma mark - 重写基类方法

- (void)setupDefaultValues {
    [super setupDefaultValues];
    self.topViewHeight = kStandardHeaderHeight; // 标准页面头部高度
}

- (void)setupHeaderView {
    YYBMacLogInfo(kTag, @"设置标准类型详情页头部视图");
    
    // 创建标准头部视图
    self.standardHeaderView = [[NSView alloc] init];
    self.standardHeaderView.wantsLayer = YES;
    self.standardHeaderView.layer.backgroundColor = [[NSColor colorWithRed:0.95 green:0.95 blue:0.95 alpha:1.0] CGColor];
    [self addSubview:self.standardHeaderView];
    
    // App图标视图
    self.appIconView = [[NSImageView alloc] init];
    self.appIconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    self.appIconView.imageAlignment = NSImageAlignCenter;
    self.appIconView.wantsLayer = YES;
    self.appIconView.layer.cornerRadius = 12.0;
    self.appIconView.layer.backgroundColor = [[NSColor lightGrayColor] CGColor];
    [self.standardHeaderView addSubview:self.appIconView];
    
    // App名称标签
    self.appNameLabel = [[NSTextField alloc] init];
    self.appNameLabel.stringValue = @"应用名称";
    self.appNameLabel.font = [NSFont systemFontOfSize:24 weight:NSFontWeightBold];
    self.appNameLabel.textColor = [NSColor labelColor];
    [self.appNameLabel setBezeled:NO];
    [self.appNameLabel setDrawsBackground:NO];
    [self.appNameLabel setEditable:NO];
    [self.appNameLabel setSelectable:NO];
    self.appNameLabel.alignment = NSTextAlignmentLeft;
    [self.standardHeaderView addSubview:self.appNameLabel];
    
    // App信息标签
    self.appInfoLabel = [[NSTextField alloc] init];
    self.appInfoLabel.stringValue = @"开发者 • 版本信息";
    self.appInfoLabel.font = [NSFont systemFontOfSize:14 weight:NSFontWeightRegular];
    self.appInfoLabel.textColor = [NSColor secondaryLabelColor];
    [self.appInfoLabel setBezeled:NO];
    [self.appInfoLabel setDrawsBackground:NO];
    [self.appInfoLabel setEditable:NO];
    [self.appInfoLabel setSelectable:NO];
    self.appInfoLabel.alignment = NSTextAlignmentLeft;
    [self.standardHeaderView addSubview:self.appInfoLabel];
}

- (void)setupContentView {
    YYBMacLogInfo(kTag, @"设置标准类型详情页内容视图");
    
    // 创建标准内容视图
    self.standardContentView = [[NSView alloc] init];
    self.standardContentView.wantsLayer = YES;
    self.standardContentView.layer.backgroundColor = [[NSColor clearColor] CGColor];
    [self addSubview:self.standardContentView];
    
    // TODO: 在这里添加标准内容，如应用描述、功能特性等
}

- (void)updateConstraints {
    [super updateConstraints];
    
//    YYBMacLogInfo(kTag, @"更新标准类型详情页约束，contentEdgeInsets: %@", NSStringFromEdgeInsets(self.contentEdgeInsets));
    
    // 标准头部视图约束
    [self.standardHeaderView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self);
        make.left.equalTo(self).offset(self.contentEdgeInsets.left);
        make.right.equalTo(self).offset(-self.contentEdgeInsets.right);
        make.height.mas_equalTo(self.topViewHeight);
    }];
    
    // App图标视图约束
    [self.appIconView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.standardHeaderView).offset(kStandardPadding);
        make.centerY.equalTo(self.standardHeaderView);
        make.width.height.mas_equalTo(80);
    }];
    
    // App名称标签约束
    [self.appNameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.appIconView.mas_right).offset(kStandardPadding);
        make.right.equalTo(self.standardHeaderView).offset(-kStandardPadding);
        make.top.equalTo(self.appIconView).offset(10);
        make.height.mas_equalTo(30);
    }];
    
    // App信息标签约束
    [self.appInfoLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.appNameLabel);
        make.top.equalTo(self.appNameLabel.mas_bottom).offset(8);
        make.height.mas_equalTo(20);
    }];
    
    // 标准内容视图约束
    [self.standardContentView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.standardHeaderView.mas_bottom).offset(kStandardPadding);
        make.left.equalTo(self).offset(self.contentEdgeInsets.left + kStandardPadding);
        make.right.equalTo(self).offset(-self.contentEdgeInsets.right - kStandardPadding);
        make.bottom.equalTo(self).offset(-kStandardPadding);
    }];
}

- (void)updateWithAppItem:(YYBAppItem *)appItem {
    [super updateWithAppItem:appItem];
    
    YYBMacLogInfo(kTag, @"更新标准类型详情页App数据: %@", appItem.name);
    
    // 更新App名称
    self.appNameLabel.stringValue = appItem.name ?: @"未知应用";
    
    // 更新App信息
    NSString *developer = appItem.developer ?: @"未知开发者";
    NSString *version = appItem.versionName ?: @"1.0.0";
    self.appInfoLabel.stringValue = [NSString stringWithFormat:@"%@ • %@", developer, version];
    
    // TODO: 加载App图标
    // TODO: 更新其他应用信息
}

@end
