//
//  GameAppDetailView.m
//  YYBMacApp
//
//  Created by l<PERSON><PERSON>lin on 23/08/2025.
//

#import "GameAppDetailView.h"
#import "MainUIDefine.h"
#import "Masonry.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

// 布局常量
static const CGFloat kGamePadding = 24.0;                // 游戏页面内边距
static const CGFloat kGameHeaderHeight = 350.0;         // 游戏头部高度

static NSString *const kTag = @"GameAppDetailView";

@interface GameAppDetailView ()

@property (nonatomic, strong) NSView *gameHeaderView;           // 游戏头部视图
@property (nonatomic, strong) NSImageView *gameScreenshotView; // 游戏截图视图
@property (nonatomic, strong) NSTextField *gameDescriptionLabel; // 游戏描述标签
@property (nonatomic, strong) NSArray *contentComponents;       // 内容组件数组

@end

@implementation GameAppDetailView

#pragma mark - 重写基类方法

- (void)setupDefaultValues {
    [super setupDefaultValues];
    self.topViewHeight = kGameHeaderHeight; // 游戏页面使用更高的头部

    // 初始化内容组件（游戏描述等）
    self.contentComponents = @[@"GameDescription"]; // 游戏描述组件
}

- (NSView *)createTopView {
    YYBMacLogInfo(kTag, @"创建游戏类型详情页头部视图");

    // 创建游戏头部视图
    self.gameHeaderView = [[NSView alloc] init];
    self.gameHeaderView.wantsLayer = YES;
    self.gameHeaderView.layer.backgroundColor = [[NSColor colorWithRed:0.05 green:0.05 blue:0.15 alpha:0.9] CGColor];

    // 游戏截图视图
    self.gameScreenshotView = [[NSImageView alloc] init];
    self.gameScreenshotView.imageScaling = NSImageScaleProportionallyUpOrDown;
    self.gameScreenshotView.imageAlignment = NSImageAlignCenter;
    self.gameScreenshotView.wantsLayer = YES;
    self.gameScreenshotView.layer.cornerRadius = 8.0;
    self.gameScreenshotView.layer.backgroundColor = [[NSColor darkGrayColor] CGColor];
    [self.gameHeaderView addSubview:self.gameScreenshotView];

    // 设置约束
    [self.gameScreenshotView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.gameHeaderView);
        make.width.mas_equalTo(300);
        make.height.mas_equalTo(200);
    }];

    // TODO: 在这里添加游戏特有的头部内容，比如游戏Logo、评分、标签等

    return self.gameHeaderView;
}

- (NSArray *)getContentComponents {
    return self.contentComponents;
}

- (NSCollectionViewItem *)createContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    YYBMacLogInfo(kTag, @"创建游戏类型详情页内容项: %@", component);

    NSCollectionViewItem *item = [self.detailCollectionView makeItemWithIdentifier:@"BaseDetailItem" forIndexPath:indexPath];

    if ([component isEqualToString:@"GameDescription"]) {
        // 创建游戏描述标签
        if (!self.gameDescriptionLabel) {
            self.gameDescriptionLabel = [[NSTextField alloc] init];
            self.gameDescriptionLabel.stringValue = @"游戏描述加载中...";
            self.gameDescriptionLabel.font = [NSFont systemFontOfSize:14 weight:NSFontWeightRegular];
            self.gameDescriptionLabel.textColor = [NSColor labelColor];
            [self.gameDescriptionLabel setBezeled:NO];
            [self.gameDescriptionLabel setDrawsBackground:NO];
            [self.gameDescriptionLabel setEditable:NO];
            [self.gameDescriptionLabel setSelectable:NO];
            self.gameDescriptionLabel.alignment = NSTextAlignmentLeft;
            self.gameDescriptionLabel.maximumNumberOfLines = 0; // 支持多行
        }

        // 将游戏描述标签添加到item的view中
        [item.view addSubview:self.gameDescriptionLabel];
        [self.gameDescriptionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(item.view).insets(NSEdgeInsetsMake(kGamePadding, kGamePadding, kGamePadding, kGamePadding));
        }];
    }

    return item;
}

- (NSSize)sizeForContentItemWithComponent:(id)component atIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = self.frame.size.width - [self leftPadding] - [self rightPadding];

    if ([component isEqualToString:@"GameDescription"]) {
        return NSMakeSize(width, 120); // 游戏描述的高度
    }

    return NSMakeSize(width, 100); // 默认高度
}



- (void)updateWithAppItem:(YYBAppItem *)appItem {
    [super updateWithAppItem:appItem];

    YYBMacLogInfo(kTag, @"更新游戏类型详情页App数据: %@", appItem.name);

    // 更新游戏描述
//    NSString *gameDescription = appItem.appDescription ?: @"这是一款精彩的游戏，为您带来无与伦比的游戏体验。";
//    self.gameDescriptionLabel.stringValue = gameDescription;

    // TODO: 加载游戏截图
    // TODO: 更新游戏特有的信息，如游戏类型、支持的设备等
}

@end

