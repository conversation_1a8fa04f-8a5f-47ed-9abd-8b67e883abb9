//
//  YYBRankComponentCardCollectionView.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//  榜单卡片容器

#import <Cocoa/Cocoa.h>
@class YYBComponent;

NS_ASSUME_NONNULL_BEGIN

@interface YYBRankComponentCardCollectionView : NSCollectionView

// cards: 数组，预期长为4，顺序: Left, Top, Bottom, Right
- (void)reloadWithComponents:(NSArray<YYBComponent *> *)components;

/// 返回自己的高度
+ (CGFloat)heightForComponents:(NSArray<YYBComponent *> *)components
                     rowIndex:(NSInteger)rowIndex
                      maxWidth:(CGFloat)maxWidth;

@end

NS_ASSUME_NONNULL_END
