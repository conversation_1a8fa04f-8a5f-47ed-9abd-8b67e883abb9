//
//  YYBRankAppListCollectionView.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBRankAppListCollectionView.h"
#import "YYBRankAppCell.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kAppListLog = @"YYBRankAppListCV";

@interface YYBRankAppListCollectionView () <NSCollectionViewDataSource, NSCollectionViewDelegate>

@property (nonatomic, copy) NSArray<YYBAppItem *> *apps;

@end

@implementation YYBRankAppListCollectionView

- (instancetype)initWithAppItems:(NSArray<YYBAppItem *> *)appItems {
    self = [super initWithFrame:NSZeroRect];
    if (self) {
//        // 初始化CollectionView布局
        NSCollectionViewFlowLayout *layout = [[NSCollectionViewFlowLayout alloc] init];
        layout.scrollDirection = NSCollectionViewScrollDirectionVertical;
        layout.itemSize = NSMakeSize(396, 80);
        layout.minimumLineSpacing = 0;
        self.collectionViewLayout = layout;

        self.dataSource = self;
        self.apps = appItems ?: @[];

        // 背景透明，方便和父视图融合
        self.backgroundColors = @[[NSColor clearColor]];

        // 注册Cell class
        [self registerClass:[YYBRankAppCell class] forItemWithIdentifier:@"YYBRankAppCell"];
    }
    return self;
}

- (void)reloadWithAppItems:(NSArray<YYBAppItem *> *)appItems {
    self.apps = appItems ?: @[];
    YYBMacLogInfo(kAppListLog, @"榜单列表reload刷新，项数=%ld", (long)self.apps.count);
    [self reloadData];
}

// MARK: CollectionView DataSource
- (NSInteger)numberOfSectionsInCollectionView:(NSCollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(NSCollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.apps.count;
}

- (NSSize)collectionView:(NSCollectionView *)collectionView layout:(NSCollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    // 防止宽的0情况加安全判断
    CGFloat width = collectionView.bounds.size.width;
    if (width <= 0) width = 396;
    return NSMakeSize(width, 80);
}

- (NSCollectionViewItem *)collectionView:(NSCollectionView *)collectionView itemForRepresentedObjectAtIndexPath:(NSIndexPath *)indexPath {
    YYBRankAppCell *cell = [collectionView makeItemWithIdentifier:@"YYBRankAppCell" forIndexPath:indexPath];
    cell.rankIndex = indexPath.item;
    [cell configureWithAppItem:self.apps[indexPath.item]];
    YYBMacLogInfo(kAppListLog, @"榜单应用 cell[%ld] app=%@", indexPath.item, self.apps[indexPath.item].name);
    return cell;
}

@end
