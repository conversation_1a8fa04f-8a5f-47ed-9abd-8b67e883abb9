//
//  YYBRankComponentCardCollectionView.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBRankComponentCardCollectionView.h"
#import "YYBRankItemsCardCell.h"
#import "YYBComponent.h"
#import "YYBBaseCardData.h"
#import "Masonry.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kCardLogTag = @"YYBRankComponentCardCollectionView";

// MARK: 自定义Layout
@interface YYBRankComponentCardLayout : NSCollectionViewLayout

@property (nonatomic, assign) NSUInteger itemCount;
@property (nonatomic, assign) NSSize collectionViewSize;
@property (nonatomic, strong) NSArray<NSValue *> *itemFrames;

@end

@implementation YYBRankComponentCardLayout

// 不同场景可以通过参数化这种Layout
- (void)prepareLayout {
    [super prepareLayout];
    NSMutableArray *frames = [NSMutableArray array];

    /*
     * 企业级Layout要求说明（已严密对齐UI设计稿）——
     * - collectionView的frame决定了所有卡片布局的总范围（blockLayout区）。
     * - 横向划分为三个等宽block（左侧大卡 / 中间上下两个小卡 / 右侧大卡）。
     * - 所有卡片顶部Y=0，底部齐平，只有中间的小卡竖向之间有24pt间隔，上下紧贴顶/底。
     * - 保证大卡/小卡纵向平齐，避免空白。
     */

    // 1. 获取 collectionView 父视图的当前尺寸，如数值过小需兜底防止初始化拉伸出错
    NSSize cvs = self.collectionView.frame.size;
    if (cvs.width < 10 || cvs.height < 10) {
        cvs = NSMakeSize(800, 400); // 缺省最低尺寸保护
    }

    // 2. 横向业务分区参数（按视觉稿）：3块等宽分布，间隔24pt
    CGFloat interBlockSpacing = 24;         // block间横向间隔
    CGFloat interSmallCardSpacing = 24;     // 两张小卡竖向间的唯一间隔

    NSUInteger totalBlocks = 3;  // 横向分3区
    CGFloat totalHorizontalSpacing = (totalBlocks - 1) * interBlockSpacing;

    // 每块理论最大宽度
    CGFloat blockWidth = (cvs.width - totalHorizontalSpacing) / totalBlocks;
    // 所有卡片纵向区域高度
    CGFloat blockHeight = cvs.height; // 注意：这里不减任何vertical spacing——大卡、小卡都要高度齐平填满parent

    // 横向block起点X（左/中/右）
    CGFloat leftBlockX = 0;
    CGFloat centerBlockX = leftBlockX + blockWidth + interBlockSpacing;
    CGFloat rightBlockX = centerBlockX + blockWidth + interBlockSpacing;

    // ----------- 纵向布局核心逻辑优化 START -------------
    /*
     * 中间两个小卡纵向堆叠，中间有24pt。左右大卡和两个小卡竖直方向都要顶到底。
     * 上小卡：Y=0，高度 = (blockHeight - interSmallCardSpacing)/2
     * 下小卡：Y=上小卡底+间隔，高度同上
     *
     * [0]左大卡:      X=leftBlockX,   Y=0, W=blockWidth,   H=blockHeight
     * [1]中-上小卡:   X=centerBlockX, Y=0, W=blockWidth,   H=smallCardHeight
     * [2]中-下小卡:   X=centerBlockX, Y=smallCardHeight+interSmallCardSpacing, W=blockWidth, H=smallCardHeight
     * [3]右大卡:      X=rightBlockX,  Y=0, W=blockWidth,   H=blockHeight
     */

    // 仅小卡之间有分割
    CGFloat smallCardHeight = (blockHeight - interSmallCardSpacing) / 2.0;

    // 5. 按顺序为每一块分配frame
    for (NSUInteger i = 0; i < self.itemCount; i++) {
        NSRect frame = NSZeroRect;
        if (i == 0) {
            // 左侧大卡
            frame = NSMakeRect(leftBlockX, 0, blockWidth, blockHeight);
        } else if (i == 1) {
            // 中间上方小卡
            frame = NSMakeRect(centerBlockX, 0, blockWidth, smallCardHeight);
        } else if (i == 2) {
            // 中间下方小卡（Y=上小卡底+间隔）
            frame = NSMakeRect(centerBlockX, smallCardHeight + interSmallCardSpacing, blockWidth, smallCardHeight);
        } else if (i == 3) {
            // 右侧大卡
            frame = NSMakeRect(rightBlockX, 0, blockWidth, blockHeight);
        } else {
            // 其它数据，暂且与左大卡同位置（如需隐藏可frame=NSZeroRect或自定义）
            frame = NSMakeRect(leftBlockX, 0, blockWidth, blockHeight);
        }

        // 6. 防守：如任意卡宽高超界则裁切
        if (NSMaxX(frame) > cvs.width) frame.size.width = cvs.width - frame.origin.x;
        if (NSMaxY(frame) > cvs.height) frame.size.height = cvs.height - frame.origin.y;

        [frames addObject:[NSValue valueWithRect:frame]];

        // 日志：每一块坐标，便于定位
        YYBMacLogInfo(kCardLogTag, @"卡%lu frame: %@ [判定:%@]", (unsigned long)i, NSStringFromRect(frame),
                      (NSMaxX(frame) > cvs.width || NSMaxY(frame) > cvs.height) ? @"超父视图" : @"正常");
    }

    self.itemFrames = frames;
    self.collectionViewSize = cvs;

    // 总体布局日志
    YYBMacLogInfo(kCardLogTag, @"卡片布局刷新: count=%ld, colSize=%@", self.itemCount, NSStringFromSize(cvs));
    // 每块详细日志已在遍历里打印
}

- (CGSize)collectionViewContentSize {
    // 内容尺寸严格不超过父视图
    return CGSizeMake(self.collectionViewSize.width, self.collectionViewSize.height);
}

- (NSArray<NSCollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(NSRect)rect {
    NSMutableArray *attributesArr = [NSMutableArray array];
    for (NSUInteger i = 0; i < self.itemFrames.count; i++) {
        NSRect frame = [self.itemFrames[i] rectValue];
        if (NSIntersectsRect(frame, rect)) {
            NSCollectionViewLayoutAttributes *attr = [NSCollectionViewLayoutAttributes layoutAttributesForItemWithIndexPath:[NSIndexPath indexPathForItem:i inSection:0]];
            attr.frame = frame;
            [attributesArr addObject:attr];
        }
    }
    return attributesArr;
}
- (NSCollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.item < self.itemFrames.count) {
        NSRect frame = [self.itemFrames[indexPath.item] rectValue];
        NSCollectionViewLayoutAttributes *attr = [NSCollectionViewLayoutAttributes layoutAttributesForItemWithIndexPath:indexPath];
        attr.frame = frame;
        return attr;
    }
    return nil;
}

@end


@interface YYBRankComponentCardCollectionView () <NSCollectionViewDataSource, NSCollectionViewDelegate>

@property (nonatomic, strong) NSArray<YYBComponent *> *components;

@end

@implementation YYBRankComponentCardCollectionView

- (instancetype)initWithFrame:(NSRect)frameRect {
    YYBRankComponentCardLayout *layout = [[YYBRankComponentCardLayout alloc] init];
    self = [super initWithFrame:frameRect];
    if (self) {
        self.collectionViewLayout = layout;
        self.backgroundColors = @[[NSColor clearColor]];

        self.dataSource = self;
        [self registerClass:[YYBRankItemsCardCell class] forItemWithIdentifier:@"YYBRankItemsCardCell"];
    }
    return self;
}

- (void)reloadWithComponents:(NSArray<YYBComponent *> *)components {
    self.components = components;
    YYBMacLogInfo(kCardLogTag, @"刷新榜单卡组：数量=%ld", components.count);
    // 更新layout数据，重载
    YYBRankComponentCardLayout *layout = (YYBRankComponentCardLayout *)self.collectionViewLayout;
    layout.itemCount = components.count;
    [layout invalidateLayout];
    [self reloadData];
}

// MARK: - NSCollectionViewDataSource
- (NSInteger)numberOfSectionsInCollectionView:(NSCollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(NSCollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.components.count;
}

- (NSCollectionViewItem *)collectionView:(NSCollectionView *)collectionView itemForRepresentedObjectAtIndexPath:(NSIndexPath *)indexPath {
    YYBRankItemsCardCell *itemCell = [collectionView makeItemWithIdentifier:@"YYBRankItemsCardCell" forIndexPath:indexPath];
    YYBComponent *data = self.components[indexPath.item];
    // 判断位置/类型，布局样式
    YYBRankItemsCardLayoutType layoutType;
    if (indexPath.item == 0 || indexPath.item == 3) {
        layoutType = YYBRankItemsCardLayoutTypeHorizontalLarge; // 大样式
    } else {
        layoutType = YYBRankItemsCardLayoutTypeVerticalSmall;   // 小卡
    }
    [itemCell configureWithData:(YYBRankCardData *)data.data layoutType:layoutType];
    YYBMacLogInfo(kCardLogTag, @"渲染榜单卡 idx=%ld cardId=%@ layoutType=%lu", indexPath.item, data.cardId, (unsigned long)layoutType);
    return itemCell;
}

/// 返回自己的高度
+ (CGFloat)heightForComponents:(NSArray<YYBComponent *> *)components
                     rowIndex:(NSInteger)rowIndex
                      maxWidth:(CGFloat)maxWidth {
    return [YYBRankItemsCardCell heightForComponents:components
                                            rowIndex:rowIndex
                                            maxWidth:maxWidth];
}

@end
