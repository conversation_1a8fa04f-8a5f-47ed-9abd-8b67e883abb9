//
//  YYBRankTagView.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/22.
//

#import "YYBRankTagView.h"
#import "Masonry.h"

@interface YYBRankTagView ()

@property (nonatomic, strong) NSImageView *iconView;
@property (nonatomic, strong) NSTextField *textLabel;

@end

@implementation YYBRankTagView

- (instancetype)initWithFrame:(NSRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.wantsLayer = YES;
        self.layer.masksToBounds = YES;
        self.iconView = [[NSImageView alloc] init];
        self.textLabel = [[NSTextField alloc] init];
        self.textLabel.editable = NO;
        self.textLabel.bezeled = NO;
        self.textLabel.backgroundColor = [NSColor clearColor];
        self.textLabel.alignment = NSTextAlignmentLeft;
        [self addSubview:self.textLabel];
    }
    return self;
}

- (void)configureWithTag:(NSString *)text
               iconNamed:(nullable NSString *)iconName
                   font:(NSFont *)font
               textColor:(NSColor *)textColor
         backgroundColor:(NSColor *)backgroundColor
            cornerRadius:(CGFloat)cornerRadius
              edgeInset:(NSEdgeInsets)edgeInset
{
    self.wantsLayer = YES;
    self.layer.cornerRadius = cornerRadius;
    self.layer.backgroundColor = backgroundColor.CGColor;

    // 移除iconView旧
    [self.iconView removeFromSuperview];

    self.textLabel.stringValue = text ?: @"";
    self.textLabel.font = font;
    self.textLabel.textColor = textColor;


    if (iconName && iconName.length) {
        NSImage *icon = [NSImage imageNamed:iconName];
        self.iconView.image = icon;
        [self addSubview:self.iconView];
        [self.iconView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self).offset(edgeInset.left);
            make.centerY.equalTo(self.textLabel);
            make.size.mas_equalTo(NSMakeSize(14, 14));
        }];
        [self.textLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.iconView.mas_right).offset(5);
            make.centerY.right.equalTo(self).insets(NSEdgeInsetsMake(0, 0, 0, edgeInset.right));
        }];
    } else {
        [self.textLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self).insets(edgeInset);
        }];
    }
}

@end
