//
//  YYBMacBlurView.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBMacBlurView.h"
#import <AppKit/AppKit.h>

#import <QuartzCore/QuartzCore.h>
#import <CoreImage/CoreImage.h>

@implementation YYBMacBlurView {
    NSImage *_customImage; // 如果你用 setImage 设置了图片
    CIContext *_ciContext;
}

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        _blurRadius = 40.0;
        _ciContext = [CIContext contextWithOptions:nil];
    }
    return self;
}

- (void)setImage:(NSImage *)image {
    _customImage = image;
    [self setNeedsDisplay:YES];
}

- (void)setBlurRadius:(CGFloat)blurRadius {
    _blurRadius = blurRadius;
    [self setNeedsDisplay:YES];
}

- (void)drawRect:(NSRect)dirtyRect {
    [super drawRect:dirtyRect];

    NSImage *sourceImage = nil;
    if (_customImage) {
        sourceImage = _customImage;
    } else {
        // 1. 截取父视图内容快照
        NSView *parent = self.superview;
        if (parent) {
            NSRect targetRect = [self convertRect:self.bounds toView:parent];
            NSBitmapImageRep *rep = [parent bitmapImageRepForCachingDisplayInRect:targetRect];
            [parent cacheDisplayInRect:targetRect toBitmapImageRep:rep];
            sourceImage = [[NSImage alloc] initWithSize:targetRect.size];
            [sourceImage addRepresentation:rep];
        }
    }

    if (sourceImage) {
        // 2. 做模糊
        CIImage *inputCI = [[CIImage alloc] initWithData:sourceImage.TIFFRepresentation];
        if (inputCI) {
            CIFilter *filter = [CIFilter filterWithName:@"CIGaussianBlur"];
            [filter setValue:inputCI forKey:kCIInputImageKey];
            [filter setValue:@(_blurRadius) forKey:kCIInputRadiusKey];
            CIImage *outputCI = filter.outputImage;
            if (outputCI) {
                // 按视图 size 绘制到 layer
                [_ciContext drawImage:outputCI
                               inRect:self.bounds
                             fromRect:outputCI.extent];
            }
        }
    }
    // 如果没图就啥也不画
}

@end
