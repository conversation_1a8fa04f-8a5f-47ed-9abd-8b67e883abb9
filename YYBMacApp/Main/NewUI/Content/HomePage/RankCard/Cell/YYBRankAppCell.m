//
//  YYBRankAppCell.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

// YYBRankAppCell.m
#import "YYBRankAppCell.h"
#import "YYBRankTagView.h"
#import "YYBAppItem.h"
#import "SDWebImage.h"
#import "Masonry.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "NSFont+YYB.h"
#import "YYBDisplayTag.h"
#import "NSImage+Utils.h"

static NSString *const kAppCellLog = @"YYBRankAppCell";

@interface YYBRankAppCell ()

@property (nonatomic, strong) NSImageView *rankImageView;
@property (nonatomic, strong) NSImageView *iconView;
@property (nonatomic, strong) NSTextField *nameLabel;
@property (nonatomic, strong) NSView *tagContainer;    // 多标签排版容器
@property (nonatomic, strong) NSMutableArray<YYBRankTagView *> *tagViews;

@end

@implementation YYBRankAppCell
#pragma mark - View Init

- (void)loadView {
    self.view = [[NSView alloc] init];
}

#pragma mark - 主要布局入口

- (void)configureWithAppItem:(YYBAppItem *)appItem {
    [self.view.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    self.view.wantsLayer = YES;
    self.view.layer.backgroundColor = [NSColor clearColor].CGColor;
    self.tagViews = [NSMutableArray array];

    if (self.cellStyle == YYBRankAppCellStyleFirstLarge) {
        [self setupFirstLargeCellWithItem:appItem];
    } else {
        [self setupNormalCellWithItem:appItem];
    }
    YYBMacLogInfo(kAppCellLog, @"应用cell渲染 rank=%lu name=%@, cate=%@", self.rankIndex+1, appItem.name, appItem.cateName);
}

#pragma mark - 榜首cell样式大头（Banner内左下角）

- (void)setupFirstLargeCellWithItem:(YYBAppItem *)appItem {
    
    // 排名-image实现
    self.rankImageView = [[NSImageView alloc] init];
    NSImage *rank = [NSImage imageNamed:@"Main/Card/sort_1"];
    self.rankImageView.image = rank;
    [self.view addSubview:self.rankImageView];
    [self.rankImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(63);
        make.bottom.equalTo(self.view);
        make.width.mas_lessThanOrEqualTo(40);
        make.height.mas_lessThanOrEqualTo(80);
    }];
    
    // 大icon
    self.iconView = [[NSImageView alloc] init];
    self.iconView.wantsLayer = YES;
    self.iconView.layer.cornerRadius = 17.4;
    self.iconView.layer.masksToBounds = YES;
    self.iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [self.view addSubview:self.iconView];
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view);
        make.bottom.equalTo(self.view);
        make.size.mas_equalTo(NSMakeSize(74, 74));
    }];
    [self.iconView sd_setImageWithURL:[NSURL URLWithString:appItem.icon] placeholderImage:nil options:SDWebImageRetryFailed context:nil];

    // App 名称
    self.nameLabel = [[NSTextField alloc] init];
    self.nameLabel.editable = NO;
    self.nameLabel.bezeled = NO;
    self.nameLabel.backgroundColor = [NSColor clearColor];
    self.nameLabel.textColor = [NSColor whiteColor];
    self.nameLabel.font = [NSFont PFMediumontWithsize:18 weight:NSFontWeightBold];
    self.nameLabel.stringValue = appItem.name ?: @"";
    self.nameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    [self.view addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(107);
        make.top.equalTo(self.iconView.mas_top).offset(20);
        make.right.lessThanOrEqualTo(self.view).offset(-10);
        make.height.mas_equalTo(24);
    }];
    
    // 标签容器, 排列最多3标签
    self.tagContainer = [[NSView alloc] init];
    [self.view addSubview:self.tagContainer];
    [self.tagContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.bottom.equalTo(self.iconView.mas_bottom).offset(-2);
        make.height.mas_equalTo(20);
        make.right.lessThanOrEqualTo(self.view).offset(-20);
    }];

    NSArray<YYBDisplayTag *> *tags = [YYBDisplayTag displayTagsForAppItem:appItem showEditorInfo:NO];
    // TODO: 根据后台给到的颜色映射表来更新text和backgroundColor的颜色
    NSInteger showTagNum = MIN(tags.count, 3);
    for (NSInteger idx = 0; idx < showTagNum; idx++) {
        YYBRankTagView *tagView = [[YYBRankTagView alloc] init];
        NSString *tagText = tags[idx].text;
        if (!tagText.length) {
            continue;
        }
        
        [tagView configureWithTag:tagText
                        iconNamed:nil
                             font:[NSFont PFMediumontWithsize:14 weight:NSFontWeightRegular]
                        textColor:[NSColor colorWithWhite:1 alpha:0.8]
                  backgroundColor:[NSColor clearColor]
                     cornerRadius:0
                        edgeInset:NSEdgeInsetsMake(2, 0, 2, 7)];

        [self.tagContainer addSubview:tagView];
        [self.tagViews addObject:tagView];

        [tagView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.tagContainer);
            if (idx == 0) {
                make.left.equalTo(self.tagContainer);
            } else {
                YYBRankTagView *preTag = self.tagViews[idx-1];
                make.left.equalTo(preTag.mas_right).offset(8);
            }
        }];

        // 标签之间加竖分隔线
        if (showTagNum > 1 && idx < showTagNum-1) {
            NSView *vertDiv = [[NSView alloc] init];
            vertDiv.wantsLayer = YES;
            vertDiv.layer.backgroundColor = [[NSColor colorWithWhite:1 alpha:0.45] CGColor];
            [self.tagContainer addSubview:vertDiv];
            [vertDiv mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(tagView.mas_right).offset(3);
                make.centerY.equalTo(tagView);
                make.height.mas_equalTo(14);
                make.width.mas_equalTo(0.5);
            }];
        }
    }
}

#pragma mark - 普通榜单cell（带标签横列表）

- (void)setupNormalCellWithItem:(YYBAppItem *)appItem {
    // 排名-image实现
    self.rankImageView = [[NSImageView alloc] init];
    NSString *rankImgePath = [NSString stringWithFormat:@"Main/Card/sort_%lu",
                              (unsigned long)(self.rankIndex + (self.cellStyle == YYBRankAppCellStyleNormal ? 1 : 2))];
    NSImage *rank = [NSImage imageNamed:rankImgePath];
    self.rankImageView.image = rank;
    [self.view addSubview:self.rankImageView];
    [self.rankImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(94);
        make.centerY.equalTo(self.view);
        make.width.mas_lessThanOrEqualTo(12);
        make.height.mas_lessThanOrEqualTo(22);
    }];

    // icon
    self.iconView = [[NSImageView alloc] init];
    self.iconView.wantsLayer = YES;
    self.iconView.layer.cornerRadius = 9; // 圆角比旧版更大
    self.iconView.layer.masksToBounds = YES;
    self.iconView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [self.view addSubview:self.iconView];
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(20);
        make.centerY.equalTo(self.view);
        make.size.mas_equalTo(NSMakeSize(56, 56));
    }];
    [self.iconView sd_setImageWithURL:[NSURL URLWithString:appItem.icon] placeholderImage:nil options:SDWebImageRetryFailed context:nil];

    // 名称
    self.nameLabel = [[NSTextField alloc] init];
    self.nameLabel.editable = NO;
    self.nameLabel.bezeled = NO;
    self.nameLabel.backgroundColor = [NSColor clearColor];
    self.nameLabel.textColor = [NSColor colorWithWhite:1 alpha:0.85];
    self.nameLabel.font = [NSFont PFMediumontWithsize:18 weight:600];
    self.nameLabel.stringValue = appItem.name ?: @"";
    self.nameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    [self.view addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(124);
        make.top.equalTo(self.iconView.mas_top).offset(2);
        make.right.lessThanOrEqualTo(self.view).offset(-20);
        make.height.mas_equalTo(24);
    }];

    // 标签容器, 排列最多3标签
    self.tagContainer = [[NSView alloc] init];
    [self.view addSubview:self.tagContainer];
    [self.tagContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.bottom.equalTo(self.iconView.mas_bottom).offset(-2);
        make.height.mas_equalTo(20);
        make.right.lessThanOrEqualTo(self.view).offset(-20);
    }];

    NSArray<YYBDisplayTag *> *tags = [YYBDisplayTag displayTagsForAppItem:appItem];
    YYBDisplayTag *firstTag = tags.firstObject;
    // TODO: 根据后台给到的颜色映射表来更新text和backgroundColor的颜色
    if (firstTag.type == YYBDisplayTagTypeEditor && firstTag.text.length) {
        // 一句话左上角的引号
        NSImageView *topTagImageView = [[NSImageView alloc] init];
        NSImage *image = [NSImage imageNamed:@"Main/Card/word_left_tag"];
        NSColor *tintColor = [NSColor colorWithRed:0.19 green:0.66 blue:1 alpha:1];
        NSColor *severColor = nil;
        //    [NSColor colorWithHexString:tagView.color];     // TODO: 后台颜色校准中，校准后使用
        topTagImageView.image = [UIImage imageWithTint:image color:severColor ?: tintColor];
        [self.tagContainer addSubview:topTagImageView];
        
        [topTagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(self.tagContainer );
            make.width.mas_equalTo(13);
            make.height.mas_equalTo(13);
        }];
        
        YYBRankTagView *tagView = [[YYBRankTagView alloc] init];
        // TODO: 根据后台给到的颜色映射表来更新text和backgroundColor的颜色
        [tagView configureWithTag:firstTag.text
                        iconNamed:nil
                             font:[NSFont PFMediumontWithsize:12 weight:NSFontWeightRegular]
                        textColor:[NSColor colorWithRed:0.19 green:0.66 blue:1 alpha:1]
                  backgroundColor:[NSColor colorWithRed:0.19 green:0.66 blue:1 alpha:0.08]
                     cornerRadius:0
                        edgeInset:NSEdgeInsetsMake(2, 0, 2, 7)];
        [self.tagContainer addSubview:tagView];
        [tagView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.tagContainer);
        }];
        return;
    }
    // TODO: 根据后台给到的颜色映射表来更新text和backgroundColor的颜色
    NSInteger showTagNum = MIN(tags.count, 3);
    for (NSInteger idx = 0; idx < showTagNum; idx++) {
        YYBRankTagView *tagView = [[YYBRankTagView alloc] init];
        NSString *tagText = tags[idx].text;
        if (!tagText.length) {
            continue;
        }
        
        [tagView configureWithTag:tagText
                        iconNamed:nil
                             font:[NSFont PFMediumontWithsize:14 weight:NSFontWeightRegular]
                        textColor:[NSColor colorWithWhite:1 alpha:0.65]
                  backgroundColor:[NSColor clearColor]
                     cornerRadius:8
                        edgeInset:NSEdgeInsetsMake(2, 0, 2, 7)];

        [self.tagContainer addSubview:tagView];
        [self.tagViews addObject:tagView];

        [tagView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.tagContainer);
            if (idx == 0) {
                make.left.equalTo(self.tagContainer);
            } else {
                YYBRankTagView *preTag = self.tagViews[idx-1];
                make.left.equalTo(preTag.mas_right).offset(8);
            }
        }];

        // 标签之间加竖分隔线
        if (showTagNum > 1 && idx < showTagNum-1) {
            NSView *vertDiv = [[NSView alloc] init];
            vertDiv.wantsLayer = YES;
            vertDiv.layer.backgroundColor = [[NSColor colorWithWhite:1 alpha:0.45] CGColor];
            [self.tagContainer addSubview:vertDiv];
            [vertDiv mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(tagView.mas_right).offset(3);
                make.centerY.equalTo(tagView);
                make.height.mas_equalTo(14);
                make.width.mas_equalTo(0.5);
            }];
        }
    }
}

@end
