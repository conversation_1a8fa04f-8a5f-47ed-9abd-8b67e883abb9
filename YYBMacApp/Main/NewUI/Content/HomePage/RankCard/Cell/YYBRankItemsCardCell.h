//
//  YYBRankItemsCardCell.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//  榜单卡Cell

#import <Cocoa/Cocoa.h>
@class YYBRankCardData;
@class YYBComponent;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, YYBRankItemsCardLayoutType) {
    YYBRankItemsCardLayoutTypeHorizontalLarge,          // 大榜单卡
    YYBRankItemsCardLayoutTypeVerticalSmall             // 小榜单卡
};

@interface YYBRankItemsCardCell : NSCollectionViewItem

- (void)configureWithData:(YYBRankCardData *)data layoutType:(YYBRankItemsCardLayoutType)layoutType;

/// 返回自己的高度
+ (CGFloat)heightForComponents:(NSArray<YYBComponent *> *)components
                     rowIndex:(NSInteger)rowIndex
                      maxWidth:(CGFloat)maxWidth;
@end

NS_ASSUME_NONNULL_END
