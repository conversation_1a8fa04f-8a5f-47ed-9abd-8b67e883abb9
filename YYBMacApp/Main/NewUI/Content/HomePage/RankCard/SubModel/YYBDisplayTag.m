//
//  YYBDisplayTag.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/22.
//

#import "YYBDisplayTag.h"
#import "YYBAppItem.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBSpecialTag.h"
#import "YYBForceTags.h"

static NSString *const kLog = @"YYBDisplayTag";

@implementation YYBDisplayTag

+ (NSArray<YYBDisplayTag *> *)displayTagsForAppItem:(YYBAppItem *)item {
    return [self displayTagsForAppItem:item showEditorInfo:YES];
}

+ (NSArray<YYBDisplayTag *> *)displayTagsForAppItem:(YYBAppItem *)item showEditorInfo:(BOOL)showEditorInfo {
    NSMutableArray<YYBDisplayTag *> *result = [NSMutableArray array];
    if (!item) {
        YYBMacLogInfo(kLog, @"[YYBGetDisplayTagsForAppItem] 入参item为nil，返回空标签");
        return @[];
    }
    if (showEditorInfo) {
        NSString *editorInfo = item.editorInfo;
        if (editorInfo && editorInfo.length > 0) {
            YYBDisplayTag *tag = [YYBDisplayTag new];
            tag.type = YYBDisplayTagTypeEditor;
            tag.text = editorInfo;
            tag.icon = nil;
            tag.colorType = 0;
            tag.origin = @"editor_info";
            [result addObject:tag];
            YYBMacLogInfo(kLog, @"[YYBGetDisplayTagsForAppItem][editor_info] 展示 editor_info: %@", editorInfo);
            return result;
        }
    }
    NSArray *forceTagsArr = item.forceTagsInfo;
    if ([forceTagsArr isKindOfClass:[NSArray class]] && forceTagsArr.count > 0) {
        for (YYBForceTags *forceTag in forceTagsArr) {
            YYBDisplayTag *tag = [YYBDisplayTag new];
            tag.type = YYBDisplayTagTypeForce;
            tag.text = forceTag.tagName;
            tag.icon = forceTag.icon;
            tag.colorType = forceTag.colorType ? forceTag.colorType.intValue : 0;
            tag.origin = @"force_tags";
            [result addObject:tag];
            YYBMacLogInfo(kLog, @"[YYBGetDisplayTagsForAppItem][force_tag] tag_name:%@ icon:%@ color_type:%zd", tag.text, tag.icon, tag.colorType);
        }
        if (result.count > 0) return result;
    }
    if ([item.specialTagInfo isKindOfClass:[NSArray class]] && item.specialTagInfo.count > 0) {
        for (YYBSpecialTag *specialTag in item.specialTagInfo) {
            YYBDisplayTag *tag = [YYBDisplayTag new];
            tag.type = YYBDisplayTagTypeSpecial;
            tag.text = specialTag.tagName ?: @"";
            tag.icon = specialTag.tagIcon ?: nil;
            tag.colorType = specialTag.colorType ? [specialTag.colorType integerValue] : 0;
            tag.origin = @"special_tag_info";
            [result addObject:tag];
            YYBMacLogInfo(kLog, @"[YYBGetDisplayTagsForAppItem][special_tag] tag_name:%@ icon:%@ color_type:%zd", tag.text, tag.icon, tag.colorType);
        }
        if (result.count > 0) return result;
    }
    NSArray<NSString *> *normalTags = nil;
    if (item.parentCateId.intValue == 2) {
        if ([item.gameTagName isKindOfClass:[NSArray class]] && item.gameTagName.count > 0) {
            normalTags = item.gameTagName;
        }
    } else {
        if ([item.showTagName isKindOfClass:[NSArray class]] && item.showTagName.count > 0) {
            normalTags = item.showTagName;
        }
    }
    if (normalTags && normalTags.count > 0) {
        for (NSString *tagStr in normalTags) {
            YYBDisplayTag *tag = [YYBDisplayTag new];
            tag.type = YYBDisplayTagTypeNormal;
            tag.text = tagStr;
            tag.icon = nil;
            tag.colorType = 0;
            tag.origin = (item.parentCateId.intValue == 2) ? @"gameTagName" : @"showTagName";
            [result addObject:tag];
            YYBMacLogInfo(kLog, @"[YYBGetDisplayTagsForAppItem][normal_tag] tag_name:%@", tag.text);
        }
        return result;
    }
    YYBMacLogInfo(kLog, @"[YYBGetDisplayTagsForAppItem] 无有效标签，返回空");
    return result;
    
}

@end
