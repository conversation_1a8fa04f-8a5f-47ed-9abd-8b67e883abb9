//
//  YYBDisplayTag.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/22.
//  标签构造

#import <Foundation/Foundation.h>

@class YYBAppItem;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, YYBDisplayTagType) {
    YYBDisplayTagTypeEditor = 1,    // 编辑标签（editor_info）
    YYBDisplayTagTypeForce = 2,     // 强制干预标签（force_tags/强干预）
    YYBDisplayTagTypeSpecial = 3,   // 特殊标签（special_tag_info）
    YYBDisplayTagTypeNormal = 4,    // 普通标签 (gameTagName/showTagName)
};

@interface YYBDisplayTag : NSObject

@property (nonatomic, assign) YYBDisplayTagType type;
@property (nonatomic, copy) NSString *text;
@property (nonatomic, copy, nullable) NSString *icon;
@property (nonatomic, assign) NSInteger colorType;
@property (nonatomic, copy) NSString *origin;

// 返回优先级最高的标签内容
+ (NSArray<YYBDisplayTag *> *)displayTagsForAppItem:(YYBAppItem *)item;

// 过滤了编辑标签，返回优先级最高的标签内容
+ (NSArray<YYBDisplayTag *> *)displayTagsForAppItem:(YYBAppItem *)item showEditorInfo:(BOOL)showEditorInfo;

@end

NS_ASSUME_NONNULL_END
