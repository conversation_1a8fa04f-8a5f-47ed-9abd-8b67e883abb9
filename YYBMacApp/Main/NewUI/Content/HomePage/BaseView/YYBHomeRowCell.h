//
//  YYBHomeRowCell.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import <Cocoa/Cocoa.h>
@class YYBComponent;

NS_ASSUME_NONNULL_BEGIN

@interface YYBHomeRowCell : NSCollectionViewItem

/// 行内渲染所有组件（横向）
- (void)reloadWithComponents:(NSArray<YYBComponent *> *)components rowIndex:(NSInteger)row;

/// 返回自己的高度
+ (CGFloat)heightForComponents:(NSArray<YYBComponent *> *)components
                     rowIndex:(NSInteger)rowIndex
                     maxWidth:(CGFloat)maxWidth;

@end

NS_ASSUME_NONNULL_END
