//
//  YYBHomeRowCell.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/21.
//

#import "YYBHomeRowCell.h"
#import "YYBComponent.h"
#import "YYBHomeBaseItem.h"
#import "YYBHomeTopItem.h"
#import "YYBHomeRankItem.h"
#import "Masonry.h"
#import <YYBMacFusionSDK/YYBMacLog.h>

static NSString *const kRowCellLogTag = @"YYBHomeRowCell";

@interface YYBHomeRowCell ()

@property (nonatomic, strong) NSMutableArray<YYBHomeBaseItem *> *itemList;
@property (nonatomic, strong) NSArray<YYBComponent *> *rowComponents;
@property (nonatomic, assign) NSInteger rowIndex;

@end

@implementation YYBHomeRowCell


- (void)viewDidLoad {
    [super viewDidLoad];
    self.itemList = [NSMutableArray array];
    self.view.wantsLayer = YES;
    
    self.view.layer.backgroundColor = [NSColor clearColor].CGColor;
}

- (void)reloadWithComponents:(NSArray<YYBComponent *> *)components rowIndex:(NSInteger)row {
    self.rowComponents = components;
    self.rowIndex = row;
    
    [self layoutCardItems];
}

- (void)layoutCardItems {
    // 清理旧view
    for (YYBHomeBaseItem *item in self.itemList) {
        [item.view removeFromSuperview];
    }
    [self.itemList removeAllObjects];
    NSUInteger count = self.rowComponents.count;
    if (count == 0) {
        return;
    }
    CGFloat totalWidth = self.view.bounds.size.width;
    CGFloat totalHeight = self.view.bounds.size.height;
    
    if ([self needCustomLayout:self.rowComponents]) {
        // 自定义分组排列样式
        YYBHomeBaseItem *item = [self createHomeItemForComponentGroup:self.rowComponents rowIndex:self.rowIndex];
        [self addChildViewController:item];
        [self.view addSubview:item.view];
        [item.view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.view);
        }];
        return;
    }
    
    // 默认横向线性排列
    CGFloat itemSpacing = 18;
    CGFloat cardWidth = (totalWidth - (count-1)*itemSpacing)/count;
    cardWidth = MAX(cardWidth, 80);
    CGFloat cardHeight = totalHeight;
    CGFloat x = 0;

    for (NSInteger i = 0; i < count; i++) {
        YYBComponent *component = self.rowComponents[i];
        YYBHomeBaseItem *item = [self createHomeItemForComponent:component rowIndex:self.rowIndex itemIndex:i];
        item.view.frame = NSMakeRect(x, 0, cardWidth, cardHeight);
        [self.view addSubview:item.view];
        [self.itemList addObject:item];
        x += (cardWidth + itemSpacing);
        YYBMacLogInfo(kRowCellLogTag, @"行%ld-卡%d渲染: cardId=%@", (long)self.rowIndex, (int)i, component.cardId);
    }
}

#pragma mark - 自定义布局判断
- (BOOL)needCustomLayout:(NSArray<YYBComponent *> *)rowComponents {
    if (rowComponents.count > 1 &&
        [rowComponents.firstObject.cardId isEqualToString:@"RankLeft"]) {
        // 榜单是自定义分组
        return YES;
    }
    
    return NO;
}

#pragma mark - 卡片类型分发工厂方法 - 默认横向线性排列
- (YYBHomeBaseItem *)createHomeItemForComponent:(YYBComponent *)component
                                       rowIndex:(NSInteger)rowIndex
                                      itemIndex:(NSInteger)itemIndex {
    // 默认用BaseItem
    YYBHomeBaseItem *item = [[YYBHomeBaseItem alloc] init];
    item.useBaseUI = NO;
    // 根据卡片类型选择item
    if ([component.cardId isEqualToString:@"RecomTopView"]) {
        item = [[YYBHomeTopItem alloc] init];
    } else if ([component.cardId isEqualToString:@"RankLeft"]) {
        item = [[YYBHomeRankItem alloc] init];
    } else {
        item.useBaseUI = YES;
    }
    [item configureWithComponent:component atRowIndex:rowIndex atItemIndex:itemIndex];
    return item;
}

#pragma mark - 卡片类型分发工厂方法 - 自定义分组排列样式
- (YYBHomeBaseItem *)createHomeItemForComponentGroup:(NSArray<YYBComponent *> *)rowComponents
                                            rowIndex:(NSInteger)rowIndex {
    // 默认用BaseItem
    YYBHomeBaseItem *item = [[YYBHomeBaseItem alloc] init];
    item.useBaseUI = NO;
    // 根据卡片类型选择item
    YYBComponent *component = rowComponents.firstObject;
    if ([component.cardId isEqualToString:@"RecomTopView"]) {
        item = [[YYBHomeTopItem alloc] init];
    } else if ([component.cardId isEqualToString:@"RankLeft"]) {
        item = [[YYBHomeRankItem alloc] init];
    } else {
        item.useBaseUI = YES;
    }
    [item configureWithComponentGroup:rowComponents atRowIndex:rowIndex];
    return item;
}


/// 返回自己的高度
+ (CGFloat)heightForComponents:(NSArray<YYBComponent *> *)components
                      rowIndex:(NSInteger)rowIndex
                      maxWidth:(CGFloat)maxWidth {
    if (components.count == 0) return [YYBHomeBaseItem heightForComponents:components
                                                                  rowIndex:rowIndex
                                                                  maxWidth:maxWidth];
    YYBComponent *first = components.firstObject;
    // 业务：以 cardId 识别主类型
    if ([first.cardId isEqualToString:@"RecomTopView"]) {
        return [YYBHomeTopItem heightForComponents:components
                                          rowIndex:rowIndex
                                          maxWidth:maxWidth];
    }
    if ([first.cardId isEqualToString:@"RankLeft"]) {
        return [YYBHomeRankItem heightForComponents:components
                                           rowIndex:rowIndex
                                           maxWidth:maxWidth];
    }
    // 其它类型映射，可渐进扩展
    return [YYBHomeBaseItem heightForComponents:components
                                       rowIndex:rowIndex
                                       maxWidth:maxWidth];
}

@end
