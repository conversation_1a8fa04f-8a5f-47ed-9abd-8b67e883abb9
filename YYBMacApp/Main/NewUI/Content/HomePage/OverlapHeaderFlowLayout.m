//
//  OverlapHeaderFlowLayout.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/21.
//

#import "OverlapHeaderFlowLayout.h"

@implementation OverlapHeaderFlowLayout
- (NSArray<NSCollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(NSRect)rect {
    NSArray *original = [super layoutAttributesForElementsInRect:rect];
    for (NSCollectionViewLayoutAttributes *attr in original) {
        if (attr.indexPath.section == 0) {
            attr.zIndex = -10; // 大于后面 section
        } else {
            attr.zIndex = 0;
        }
    }
    return original;
}@end
