//
//  DownloadBoxPopver.m
//  YYBMacApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/21.
//

#import "DownloadBoxPopver.h"
#import "SDWebImage.h"
#import "YYBApkInfoViewModel.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBLibAria2ServiceFacade.h"
#import "InstallApkInfo.h"
#import "YYBApkPackage.h"
#import "YYBApkPackage.h"
#import "YYBAria2DownloadManager.h"
#import "YYBAPKInfoModel.h"
#import "YYBApkInfoViewModelPool.h"

static NSString *const kTag = @"DownloadBoxPopver";

#define OVERLAY_WIDTH 350.0
#define OVERLAY_HEIGHT 420.0
#define ROW_HEIGHT 90.0
#define HEADER_HEIGHT 50.0
#define MAX_VISIBLE_ROWS 4

#pragma mark - Custom Table Cell
@interface PendingInstallCell : NSTableCellView  <YYBApkInfoViewModelDelegate>
@property (strong) NSImageView *iconView;
@property (strong) NSTextField *nameLabel;
@property (strong) NSTextField *sizeLabel;
@property (strong) NSTextField *statusLabel;
@property (strong) NSButton *pauseButton;
@property (strong) NSButton *cancelButton;
@property (strong) NSImage *pauseIcon;
@property (strong) NSImage *playIcon;
@property (strong) NSProgressIndicator *progressIndicator;

@property (nonatomic, strong, readwrite) YYBApkInfoViewModel *viewModel; //下载安装管理器
@property (nonatomic, strong, readonly) YYBApkInfoModel *model;

@property (weak) id<PendingInstallCellDelegate> delegate;

- (void)setPkgName:(NSString *)pkgName;

@end

@implementation PendingInstallCell

- (instancetype)initWithFrame:(NSRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        // Icon View
        _iconView = [[NSImageView alloc] initWithFrame:NSMakeRect(10, 15, 69, 69)];
        _iconView.layer.cornerRadius = 5;
        _iconView.wantsLayer = YES;
        [_iconView sd_setImageWithURL:[NSURL URLWithString:@"https://pp.myapp.com/ma_icon/0/icon_10485956_1741244516/0"]];
        [self addSubview:_iconView];
        
        // Name Label
        _nameLabel = [NSTextField labelWithString:@""];
        _nameLabel.font = [NSFont systemFontOfSize:14];
        _nameLabel.frame = NSMakeRect(95, ROW_HEIGHT -35, 170, 20);
        [self addSubview:_nameLabel];
        
        
        // Progress Indicator
        _progressIndicator = [[NSProgressIndicator alloc] initWithFrame:NSMakeRect(95, ROW_HEIGHT - 50, 158, 10)];
        _progressIndicator.style = NSProgressIndicatorStyleBar;
        _progressIndicator.controlSize = NSControlSizeSmall;
        _progressIndicator.indeterminate = NO;
        [_progressIndicator setMinValue:0.0];
        [_progressIndicator setMaxValue:100.0];
        [self addSubview:_progressIndicator];
        
        // Pause Button
        // 暂停图标
        _pauseIcon = [NSImage imageNamed:@"Main/DownloadBox/icon_pause"];
        // 播放图标（暂停状态下显示播放）
        _playIcon = [NSImage imageNamed:@"Main/DownloadBox/icon_play"];
        
        _pauseButton = [NSButton buttonWithImage:_pauseIcon target:self action:@selector(pauseClicked:)];
        _pauseButton.image = _pauseIcon;
        _pauseButton.identifier = @"pause";
        _pauseButton.imagePosition = NSImageOnly;  // 仅显示图标
        _pauseButton.bezelStyle = NSBezelStyleInline;
        _pauseButton.imageScaling = NSImageScaleProportionallyDown; // 图片自适应
        _pauseButton.wantsLayer = YES;
        _pauseButton.layer.backgroundColor = [NSColor clearColor].CGColor;
        _pauseButton.focusRingType = NSFocusRingTypeNone; // 禁用焦点高亮环
        _pauseButton.frame = NSMakeRect(263, ROW_HEIGHT - 50, 20, 20);
        [self addSubview:_pauseButton];
       
        
        
        // Cancel Button
        _cancelButton = [NSButton buttonWithImage:[NSImage imageNamed:@"Main/DownloadBox/icon_close"] target:self action:@selector(cancelClicked:)];
        _cancelButton.frame = NSMakeRect(294, ROW_HEIGHT - 50, 20, 20);
        _cancelButton.imagePosition = NSImageOnly;  // 仅显示图标
        _cancelButton.bezelStyle = NSBezelStyleInline;
        _cancelButton.imageScaling = NSImageScaleProportionallyDown; // 图片自适应
        _cancelButton.wantsLayer = YES;
        _cancelButton.layer.backgroundColor = [NSColor clearColor].CGColor;
        _cancelButton.focusRingType = NSFocusRingTypeNone; // 禁用焦点高亮环
        [self addSubview:_cancelButton];
        
        // Size Label
        _sizeLabel = [NSTextField labelWithString:@""];
        _sizeLabel.font = [NSFont systemFontOfSize:12];
        _sizeLabel.textColor = [NSColor secondaryLabelColor];
        _sizeLabel.frame = NSMakeRect(95, 16, 70, 16);
        [self addSubview:_sizeLabel];
        
        //statusLabel
        _statusLabel = [NSTextField labelWithString:@"暂停中"];
        _statusLabel.font = [NSFont systemFontOfSize:12];
        _statusLabel.textColor = [NSColor secondaryLabelColor];
        _statusLabel.frame = NSMakeRect(155, 16, 70, 16);
        [self addSubview:_statusLabel];
        
       
    }
    return self;
}

- (void)pauseClicked:(NSButton *)sender {
    if ([sender.identifier isEqualToString:@"pause"]) {
        sender.identifier = @"play";
        sender.image = _playIcon;
        self.statusLabel.stringValue = @"暂停中";
        //暂停下载
        YYBAria2Task *task = self.viewModel.downloadApkInfo;
        if (task) {
            YYBMacLogInfo(kTag, @"[pauseClicked] 暂停--apk,  pkg=%@", self.viewModel.model.name);
            [self pausApk:task];
        }
       
    } else {
        sender.identifier = @"pause";
        sender.image = _pauseIcon;
       
        //继续下载
        YYBAria2Task *task = self.viewModel.downloadApkInfo;
        if (task) {
            YYBMacLogInfo(kTag, @"[continueClicked] 继续--apk,  pkg=%@", self.viewModel.model.name);
            [self continueApk:task];
        }
    }
}

- (void)pausApk:(YYBAria2Task *)task {
    [[YYBAria2DownloadManager sharedManager] pauseTask:task completion:^(BOOL success, NSError * _Nullable error) {
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] pause result=%d error=%@", success, error);
    }];
}

- (void)continueApk:(YYBAria2Task *)task {
    [[YYBAria2DownloadManager sharedManager] resumeTask:task completion:^(BOOL success, NSError * _Nullable error) {
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] resume(recoverable) result=%d error=%@", success, error);
    }];
}


- (void)cancelClicked:(NSButton *)sender {
    // Notify delegate about cancel
    NSTableView *tableView = (NSTableView *)self.superview.superview;
    NSInteger row = [tableView rowForView:self];
    if (row != -1 && [self.delegate respondsToSelector:@selector(installDidCancelAtRow:)]) {
        [self.delegate installDidCancelAtRow:row];
    }
}

#pragma mark - 绑定VM
- (void)setPkgName:(NSString *)pkgName {
    if (!pkgName) {
        YYBMacLogInfo(kTag, @"[setPkgName] pkgName is nil, ignore");
        return;
    }
    if (self.viewModel) {
        [self.viewModel removeDelegate:self];
    }
    self.viewModel = [[YYBApkInfoViewModelPool sharedPool] viewModelForPkgName:pkgName];
    [self.viewModel addDelegate:self];
    YYBMacLogInfo(kTag, @"[setPkgName bind] pkg=%@", pkgName);
    [self refreshUIWithState:self.model.apkState progress:self.model.downloadPercent error:nil];
}

#pragma mark - VM Delegate（主线程回调）
- (void)apkInfoViewModel:(YYBApkInfoViewModel *)viewModel didUpdateModel:(YYBApkInfoModel *)model error:(NSError *)error {
    [self refreshUIWithState:model.apkState progress:model.downloadPercent error:error];
}

//安装
- (void)installApk {
    InstallApkInfo *installApkInfo = [InstallApkInfo createInstallApkInfoFromApkInfo:self.model
                                                                     downloadApkInfo:self.viewModel.downloadApkInfo];
    [[YYBApkPackage shared] installApp:installApkInfo completion:^(InstallApkInfo * _Nullable apkInfo, NSInteger retCode, NSString * _Nullable errorMessage) {
        YYBMacLogInfo(kTag, @"[installApk] install code=%ld msg=%@", (long)retCode, errorMessage);
    }];
}

//打开
- (void)openApk {
    [[YYBApkPackage shared] openApp:self.model.pkgName];
}

#pragma mark - UI 刷新（状态 → 文案/样式 映射）
- (void)refreshUIWithState:(NSString *)apkState progress:(double)progress error:(NSError * _Nullable)error {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshUIWithState:apkState progress:progress error:error];
        });
        return;
    }
    // 分发状态到子控件，由子控件自己解析状态渲染内容
    [self setApkState:apkState
                       autoStart:self.viewModel.autoStart
                       haveCache:self.viewModel.haveCache
                        progress:progress
                           error:error];
    YYBMacLogInfo(kTag, @"[refreshUI] pkg=%@ state=%@ progress=%.2f%%", self.model.pkgName, apkState, progress*100);
}

#pragma mark - 状态综合控制接口（建议外部只用本接口驱动UI）
- (void)setApkState:(NSString *)apkState
          autoStart:(BOOL)autoStart
          haveCache:(BOOL)haveCache
           progress:(double)progress
              error:(NSError * _Nullable)error{
    // 默认
    BOOL pauseButtonEnabled = YES;
    NSString *stausText = @"";
    
        // 各种状态(非自动打开)
        if ([apkState isEqualToString:YYBApkStateInstalled]) {
            stausText = @"安装完成";
        } else if ([apkState isEqualToString:YYBApkStateFetchingApkInfo] ||
                   [apkState isEqualToString:YYBApkStateInstalling]) {
            // 请求信息中或安装中，展示loading, 且不可再点击
            stausText = @"安装中";
            pauseButtonEnabled = NO;
        } else if ([apkState isEqualToString:YYBApkStateWaiting]) {
            stausText = @"等待中";
        } else if ([apkState isEqualToString:YYBApkStatePaused]) {
            stausText = @"暂停中";
        }
        else if ([apkState isEqualToString:YYBApkStateActive]) {
            stausText = @"下载中";
        } else {
            stausText = @"暂停中";
        }
        self.pauseButton.enabled = pauseButtonEnabled;
        self.statusLabel.stringValue = stausText;
        if (progress> 0) {
            self.progressIndicator.doubleValue = progress*100;
        }
       
}

@end




#pragma mark - Main Overlay Implementation
@interface DownloadBoxPopver () <NSTableViewDelegate, NSTableViewDataSource>
@property (strong) NSTableView *tableView;
@property (strong) NSVisualEffectView *backgroundView;
@property (strong) NSView *emptyView;

@property (strong) NSTextField *titleLabel;
@property (assign) NSInteger currentItemsCount;
@property (strong, nonatomic) NSClickGestureRecognizer *outsideClickGesture;
@end

@implementation DownloadBoxPopver

#pragma mark - PendingCellDelegate
- (void)installDidCancelAtRow:(NSInteger)row {
    if (row < 0 || row >= self.installItems.count) return;
    
    // Remove the item from the data source
    [self.installItems removeObjectAtIndex:row];
    
    if (self.installItems.count == 0) {
        [self showEmptyView];
        
    } else {
        _titleLabel.stringValue = [NSString stringWithFormat:@"安装中 (%ld)", self.installItems.count];
    }
        
    // Update the UI with animation
    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = 0.3;
        [self.tableView removeRowsAtIndexes:[NSIndexSet indexSetWithIndex:row] withAnimation:NSTableViewAnimationSlideUp];
        
    }];
}

- (void)showEmptyView {
    _titleLabel.stringValue= @"";
    if (!self.emptyView) {
        NSRect frame = NSMakeRect(0, 0, OVERLAY_WIDTH, OVERLAY_HEIGHT);
        _emptyView = [[NSView alloc] initWithFrame:frame];
        _emptyView.wantsLayer = YES;
        NSImageView  *imageView = [[NSImageView alloc] initWithFrame:NSMakeRect(OVERLAY_WIDTH/2 -29, OVERLAY_HEIGHT/2 -20, 58, 58)];
        imageView.image = [NSImage imageNamed:@"Main/DownloadBox/icon_box"];
        [_emptyView addSubview:imageView];
        
        NSTextField *textField = [[NSTextField alloc] initWithFrame:NSMakeRect(OVERLAY_WIDTH/2 -60, OVERLAY_HEIGHT/2 -45, 120, 20)];
        textField.stringValue = @"当前没有安装任务";
        textField.textColor = [NSColor secondaryLabelColor];
        textField.font = [NSFont systemFontOfSize:12];
        textField.alignment = NSTextAlignmentCenter;
        textField.backgroundColor = [UIColor clearColor];
        textField.bordered = NO;
        textField.drawsBackground = NO;
        [_emptyView addSubview:textField];
        [_backgroundView addSubview:_emptyView];
    } else {
        _emptyView.hidden = NO;
    }
}

- (instancetype)init{
    self = [super initWithNibName:nil bundle:nil];
    if (self) {
        
        _installItems = [NSMutableArray array];
        //获取下载中+待安装列表
        NSArray *installedApps = [YYBApkPackage shared].installedApps;
        NSArray *downloadApkInfos = [[YYBAria2DownloadManager sharedManager] allTasks];
        for (YYBAria2Task *task in downloadApkInfos) {
            //过滤非apk任务
            if ([task.pkgName isEqualToString:@""] || task.pkgName == nil ||[task.apkName isEqualToString:@""] || task.apkName == nil) {
                continue;
            }
            //过滤已安装的应用
            bool isInstalled = NO;
            for (InstallApkInfo *installApkInfo in installedApps) {
                if ([installApkInfo.pkgName isEqualToString:task.pkgName]) {
                    isInstalled = YES;
                    break;
                }
            }
            if (!isInstalled) {
                [_installItems addObject:task];
                YYBMacLogInfo(kTag, @"[init] 过滤出下载盒子展示 pkgName=%@", task.pkgName);
            }
           
        }
        
        _currentItemsCount = installedApps.count;
        
        YYBMacLogInfo(kTag, @"[init] installItems count=%ld", _installItems.count);
        
    }
    return self;
}

- (void)loadView {
   
    // Background View (Visual Effect View)
    NSRect frame = NSMakeRect(0, 0, OVERLAY_WIDTH, OVERLAY_HEIGHT);
    _backgroundView = [[NSVisualEffectView alloc] initWithFrame:frame];
    _backgroundView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    _backgroundView.material = NSVisualEffectMaterialMenu;
    _backgroundView.state = NSVisualEffectStateActive;
    _backgroundView.wantsLayer = YES;
    _backgroundView.layer.cornerRadius = 5.0;
    _backgroundView.layer.masksToBounds = YES;
    
    // Title Label
    _titleLabel = [NSTextField labelWithString:[NSString stringWithFormat:@"正在安装（%ld）",_installItems.count]];
    _titleLabel.font = [NSFont boldSystemFontOfSize:16];
    _titleLabel.frame = NSMakeRect(15, OVERLAY_HEIGHT - HEADER_HEIGHT - 15, OVERLAY_WIDTH - 30, HEADER_HEIGHT);
    _titleLabel.autoresizingMask = NSViewMinYMargin;
    [_backgroundView addSubview:_titleLabel];
    
    // ScrollView Container
    NSScrollView *scrollView = [[NSScrollView alloc] initWithFrame:NSMakeRect(
        0, 0, OVERLAY_WIDTH, OVERLAY_HEIGHT - HEADER_HEIGHT
    )];
    scrollView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;
    scrollView.drawsBackground = NO;
    
    // TableView
    _tableView = [[NSTableView alloc] initWithFrame:scrollView.bounds];
    _tableView.autoresizingMask = NSViewWidthSizable;
    _tableView.backgroundColor = [NSColor clearColor];
    _tableView.headerView = nil;
    _tableView.rowHeight = ROW_HEIGHT;
    _tableView.selectionHighlightStyle = NSTableViewSelectionHighlightStyleNone;
    _tableView.dataSource = self;
    _tableView.delegate = self;
    
    // Single Column
    NSTableColumn *column = [[NSTableColumn alloc] initWithIdentifier:@"InstallColumn"];
    column.width = OVERLAY_WIDTH;
    [_tableView addTableColumn:column];
    
    scrollView.documentView = _tableView;
    [_backgroundView addSubview:scrollView];
    
    self.view = _backgroundView;
    
    if (_installItems.count == 0) {
        [self showEmptyView];
    }
}

- (void)showInView:(NSView *)parentView atPoint:(CGPoint)position {
    if (!self.view.superview) {
        
        // 添加点击外部关闭的手势
           _outsideClickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleOutsideClick:)];
           _outsideClickGesture.numberOfClicksRequired = 1;
           _outsideClickGesture.delaysPrimaryMouseButtonEvents = NO; // 允许事件继续传递
           [parentView addGestureRecognizer:_outsideClickGesture];
        
        
        // Position the view
        NSRect frame = self.view.frame;
        frame.origin.x = position.x - frame.size.width -20;
        frame.origin.y = position.y - frame.size.height - 90;
        self.view.frame = frame;
        
        // Add to parent view
        [parentView addSubview:self.view];
        
        // Fade in animation
        self.view.alphaValue = 0;
        [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
            context.duration = 0.25;
            self.view.animator.alphaValue = 1.0;
        }];
        
    }
}


- (void)handleOutsideClick:(NSClickGestureRecognizer *)gesture {
    NSPoint location = [gesture locationInView:self.view.superview];
    if (!NSPointInRect(location, self.view.frame)) {
        [self dismiss];
        // 移除手势识别器
        if (self.outsideClickGesture) {
            [self.view.superview removeGestureRecognizer:self.outsideClickGesture];
            self.outsideClickGesture = nil; // 释放资源
        }
    }
    
}

- (void)dismiss {
    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = 0.25;
        self.view.animator.alphaValue = 0;
    } completionHandler:^{
        [self.view removeFromSuperview];
    }];
}


#pragma mark - TableView DataSource
- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return self.installItems.count;
}

- (NSView *)tableView:(NSTableView *)tableView viewForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    PendingInstallCell *cell = [tableView makeViewWithIdentifier:@"InstallCell" owner:self];
    if (!cell) {
        cell = [[PendingInstallCell alloc] initWithFrame:NSMakeRect(0, 0, OVERLAY_WIDTH, ROW_HEIGHT)];
        cell.identifier = @"InstallCell";
    }
    
    YYBAria2Task *task = self.installItems[row];
    cell.nameLabel.stringValue = task.apkName;
    cell.sizeLabel.stringValue = [self formatFileSizeWithPrecision:task.totalLength];
    cell.statusLabel.stringValue = task.statusString;
    if (task.status == YYBAria2TaskStatusActive) {
        cell.pauseButton.image = cell.pauseIcon;
        cell.pauseButton.identifier = @"pause";
    } else {
        cell.pauseButton.image = cell.playIcon;
        cell.pauseButton.identifier = @"play";
    }
    
    [cell.progressIndicator setDoubleValue:task.progress*100];
    [cell.iconView sd_setImageWithURL:[NSURL URLWithString:task.apkIcon]];
    cell.delegate = self;
    [cell setPkgName:task.pkgName];
    
    return cell;
}

///格式化文件大小
-(NSString*) formatFileSizeWithPrecision:(long long) fileSize {
    if (fileSize < 1024) {
        return [NSString stringWithFormat:@"%lld B", fileSize];
    }
    
    NSArray *units = @[@"KB", @"M", @"G", @"T"];
    NSArray *thresholds = @[
        [NSNumber numberWithLongLong:1024],                     // 1 KB
        [NSNumber numberWithLongLong:1024 * 1024],              // 1 MB
        [NSNumber numberWithLongLong:1024LL * 1024LL * 1024LL], // 1 GB
        [NSNumber numberWithLongLong:1024LL * 1024LL * 1024LL * 1024LL] // 1 TB
    ];
    double value = (double)fileSize;
    
    for (int i = 0; i < thresholds.count; i++) {
        long long threshold = [thresholds[i] longLongValue];
        if (fileSize < threshold) {
            value /= (i > 0 ? [thresholds[i-1] doubleValue] : 1024);
            return [NSString stringWithFormat:@"%.*f %@", 2, value, units[i-1]];
        }
    }
    
    // 超过TB的处理
    return [NSString stringWithFormat:@"%.*f T", 2, (double)fileSize /(1024LL * 1024LL * 1024LL * 1024LL)];
}

#pragma mark - Updates
- (void)updateItems:(NSMutableArray<YYBAria2Task *> *)newItems {
    self.installItems = newItems;
    
    // Animate changes if item count changed
    if (self.currentItemsCount != newItems.count) {
        [NSAnimationContext beginGrouping];
        [NSAnimationContext currentContext].duration = 0.3;
        
        [self.tableView beginUpdates];
        [self.tableView reloadData];
        [self.tableView endUpdates];
        
        // Resize overlay height
        CGFloat visibleRows = MIN(MAX_VISIBLE_ROWS, newItems.count);
        CGFloat totalHeight = HEADER_HEIGHT + (visibleRows * ROW_HEIGHT);
        
        NSRect newFrame = self.view.frame;
        newFrame.size.height = totalHeight;
        self.view.animator.frame = newFrame;
        
        [NSAnimationContext endGrouping];
        
        self.currentItemsCount = newItems.count;
    } else {
        [self.tableView reloadData];
    }
}

@end
