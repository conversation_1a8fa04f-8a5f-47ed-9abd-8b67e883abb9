//
//  SettingPopver.m
//  YYBMacApp
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/20.
//

#import "SettingPopverView.h"

#define OVERLAY_HEIGHT 160
#define OVERLAY_WIDTH 250.0
#define ROW_HEIGHT 32.0

@interface SettingPopverView () {
    NSStackView *_mainStackView;
    NSVisualEffectView *_backgroundView;
    NSArray<NSString *> *_menuItems;
}

@property (strong) NSImageView *userAvatar;
@property (strong) NSTextField *usernameLabel;
@property (strong) NSTextField *versionLabel;
@property (strong) NSMutableArray<NSButton *> *menuButtons;
@property (strong, nonatomic) NSClickGestureRecognizer *outsideClickGesture;

@end

@implementation SettingPopverView

- (instancetype)init {
    self = [super init];
    if (self) {
        _menuItems = @[@"反馈", @"设置",@"退出登录"];
        _menuButtons = [NSMutableArray array];
        
        // 使用手动布局
        self.translatesAutoresizingMaskIntoConstraints = YES;
        
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 背景视图
    _backgroundView = [[NSVisualEffectView alloc] initWithFrame:NSMakeRect(0, 0, OVERLAY_WIDTH, 200)];
    _backgroundView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    _backgroundView.material = NSVisualEffectMaterialMenu;
    _backgroundView.state = NSVisualEffectStateActive;
    _backgroundView.wantsLayer = YES;
    _backgroundView.layer.cornerRadius = 5.0;
    _backgroundView.layer.masksToBounds = YES;
    [self addSubview:_backgroundView];
    
    // 用户信息区域
    [self setupUserSection];
    
    // 版本信息区域
    [self setupVersionSection];
    
    // 添加菜单项
    CGFloat currentY = OVERLAY_HEIGHT - 74; // 从顶部开始向下布局
    for (NSString *title in _menuItems) {
        [self addMenuItem:title atY:currentY];
        currentY -= ROW_HEIGHT; // 每个菜单项高度32
    }
}

//用户信息
- (void)setupUserSection {
    NSView *userSection = [[NSView alloc] initWithFrame:NSMakeRect(10, OVERLAY_HEIGHT - 10, OVERLAY_WIDTH - 20, ROW_HEIGHT)];
    
    // 用户名
    _usernameLabel = [NSTextField labelWithString:@"sara"];
    _usernameLabel.frame = NSMakeRect(10, 8, 100, 16);
    _usernameLabel.font = [NSFont systemFontOfSize:13 weight:NSFontWeightMedium];
    [_usernameLabel sizeToFit]; // 让label根据内容自适应宽度
    
    // 用户头像 - 位置根据用户名宽度来设置
    NSImage *avatarImage = [NSImage imageNamed:NSImageNameUser];
    avatarImage.size = NSMakeSize(13, 13);
    _userAvatar = [[NSImageView alloc] initWithFrame:NSMakeRect(
        NSMaxX(_usernameLabel.frame) + 5, // username右边缘 + 5像素间距
        8,
        13,
        13)];
    _userAvatar.image = avatarImage;
    
    [userSection addSubview:_usernameLabel];
    [userSection addSubview:_userAvatar];
    [_backgroundView addSubview:userSection];
    
    // 添加悬停效果
    NSTrackingArea *trackingArea = [[NSTrackingArea alloc] initWithRect:userSection.bounds
                                                              options:(NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingInVisibleRect)
                                                                owner:self
                                                             userInfo:@{@"button": userSection}];
    [userSection addTrackingArea:trackingArea];
}

//版本信息
- (void)setupVersionSection {
    NSView *versionSection = [[NSView alloc] initWithFrame:NSMakeRect(10, OVERLAY_HEIGHT - ROW_HEIGHT - 10, OVERLAY_WIDTH - 20, ROW_HEIGHT)];
    
    NSTextField *aboutLabel = [NSTextField labelWithString:@"关于"];
    aboutLabel.frame = NSMakeRect(10, 8, 100, 16);
    aboutLabel.font = [NSFont systemFontOfSize:13];
    
    _versionLabel = [NSTextField labelWithString:@"1.0"];
    _versionLabel.frame = NSMakeRect(200, 8, 40, 16);
    _versionLabel.font = [NSFont systemFontOfSize:13];
    
    [versionSection addSubview:aboutLabel];
    [versionSection addSubview:_versionLabel];
    [_backgroundView addSubview:versionSection];
    
    NSTrackingArea *trackingArea = [[NSTrackingArea alloc] initWithRect:versionSection.bounds
                                                              options:(NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingInVisibleRect)
                                                                owner:self
                                                             userInfo:@{@"button": versionSection}];
    [versionSection addTrackingArea:trackingArea];
}


//其他扩展入口
- (void)addMenuItem:(NSString *)title atY:(CGFloat)yPosition {
    NSView *menuItemContainer = [[NSView alloc] initWithFrame:NSMakeRect(10, yPosition, OVERLAY_WIDTH - 20, ROW_HEIGHT)];
    menuItemContainer.wantsLayer = YES;
    
    NSButton *button = [NSButton buttonWithTitle:title target:self action:@selector(menuItemAction:)];
    button.frame = NSMakeRect(10, 0, 210, 32);
    button.tag = [_menuItems indexOfObject:title];
    button.bezelStyle = NSBezelStyleInline;
    button.contentTintColor = [NSColor whiteColor];
    button.font = [NSFont systemFontOfSize:13];
    button.alignment = NSTextAlignmentLeft;
    button.bordered = NO;
    
    [menuItemContainer addSubview:button];
    [self.menuButtons addObject:button];
    [_backgroundView addSubview:menuItemContainer];
    
    NSTrackingArea *trackingArea = [[NSTrackingArea alloc] initWithRect:menuItemContainer.bounds
                                                              options:(NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingInVisibleRect)
                                                                owner:self
                                                             userInfo:@{@"button": menuItemContainer}];
    [menuItemContainer addTrackingArea:trackingArea];
}

- (void)menuItemAction:(NSButton *)sender {
    NSInteger index = sender.tag;
    if (index >= 0 && index < _menuItems.count) {
        NSString *title = _menuItems[index];
        if (self.onMenuItemSelected) {
            self.onMenuItemSelected(title);
        }
    }
}

- (void)logoutAction {
    if (self.onLogout) {
        self.onLogout();
    }
}


- (void)showFromView:(NSView *)view inWindow:(NSWindow *)window {
    // 计算弹窗位置
    NSRect windowFrame = window.frame;
    self.frame = NSMakeRect(windowFrame.size.width - 270,
                           windowFrame.size.height - 290,
                           250,
                           200);
    
    [window.contentView addSubview:self];
    
    // 动画效果
    self.alphaValue = 0;
    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = 0.25;
        self.animator.alphaValue = 1.0;
    }];
    
    // 添加点击外部关闭手势
    _outsideClickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleOutsideClick:)];
    _outsideClickGesture.numberOfClicksRequired = 1;
    _outsideClickGesture.delaysPrimaryMouseButtonEvents = NO;
    [window.contentView addGestureRecognizer:_outsideClickGesture];
}


- (void)handleOutsideClick:(NSClickGestureRecognizer *)gesture {
    NSPoint location = [gesture locationInView:self.superview];
    if (!NSPointInRect(location, self.frame)) {
        [self dismiss];
        // 移除手势识别器
        if (self.outsideClickGesture) {
            [self.window.contentView removeGestureRecognizer:self.outsideClickGesture];
            self.outsideClickGesture = nil; // 释放资源
        }
    }
    
}

- (void)dismiss {
    // 移除手势识别器
    if (self.outsideClickGesture) {
        [self.window.contentView removeGestureRecognizer:self.outsideClickGesture];
        self.outsideClickGesture = nil; // 释放资源
    }
    
    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = 0.5;
        self.animator.alphaValue = 0;
    } completionHandler:^{
        [self removeFromSuperview];
    }];
}


#pragma mark - 鼠标悬停处理
- (void)mouseEntered:(NSEvent *)event {
    NSView *button = event.trackingArea.userInfo[@"button"];
    if (button) {
        // 添加悬停背景
        button.wantsLayer = YES;
        button.layer.backgroundColor = [NSColor colorWithRed:255 green:255 blue:255 alpha:0.08].CGColor;
        button.layer.cornerRadius = 4.0;
        
        // 添加动画效果
        [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
            context.duration = 0.2;
            button.animator.alphaValue = 1.0;
        }];
    }
}

- (void)mouseExited:(NSEvent *)event {
    NSView *button = event.trackingArea.userInfo[@"button"];
    if (button) {
        // 移除悬停背景
        button.layer.backgroundColor = [NSColor clearColor].CGColor;
        
        // 添加动画效果
        [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
            context.duration = 0.2;
            button.animator.alphaValue = 1.0;
        }];
    }
}

@end
