//
//  MainViewController.m
//  YYBMacApp
//
//  Created by halehuang on 2025/7/7.
//

#import <YYBMacFusionSDK/YYBMacMMKV.h>
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <QuartzCore/QuartzCore.h>
#import "MainViewController.h"
#import "YYBDefine.h"
#import "Masonry.h"
#import "YYBMainNavigationView.h"
#import "YYBMainContentView.h"
#import "YYBMainTopbarView.h"
#import "MainUIDefine.h"
#import "SettingPopverView.h"
#import "DownloadBoxPopver.h"
#import "YYBAppLaunchHistoryView.h"

static NSString *const tag = @"MainViewController";

@interface MainViewController () <YYBMainNavigationViewDataSource, YYBMainNavigationViewDelegate, YYBMainTopbarViewDelegate, YYBMainTopbarViewDataSource, YYBMainContentViewDelegate>

@property (nonatomic, strong) YYBMainNavigationView *navigationView;    // 左侧导航栏
@property (nonatomic, strong) YYBMainContentView *contentView;          // 右侧内容区
@property (nonatomic, strong) YYBMainTopbarView *topToolbarView;        // 顶部工具栏

@property (nonatomic, strong) YYBMainNavigationModel *navigationModel;  // 导航栏数据模型
@property (nonatomic, strong) YYBTopbarModel *topbarModel;              // 顶部工具栏数据模型

@property (strong) SettingPopverView *settingPoperView;                 //设置浮层
@property (strong) DownloadBoxPopver *downloadBoxPoperView;             //下载盒子浮层

@property (nonatomic, strong) MASConstraint *navigationViewWidthConstraint; // 导航栏宽度约束

@end

@implementation MainViewController

- (instancetype)init {
    if (self = [super init]) {
    
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.wantsLayer = YES;
    self.view.layer.backgroundColor = [NSColor clearColor].CGColor;
    
    [self setupModel];
    [self setupUI];
    
    [self.navigationView selectItem:self.navigationModel.items.firstObject];
}

- (void)setupModel {
    [self setupNavigationModel];
    [self setupTopbarModel];
}

- (void)setupUI {
    [self setupNavigationView];
    [self setupTopToolbarView];
    [self setupContentView];
}

#pragma mark - Model
- (void)setupNavigationModel {
    self.navigationModel = [[YYBMainNavigationModel alloc] init];
    {
        YYBMainNavigationItem *item = [YYBMainNavigationItem itemWithIdentifier:@"home"
                                                                          title:@"首页"
                                                                           icon:[NSImage imageNamed:@"Main/Navigation/HomeIcon"]
                                                                      isWebView:NO];
        [self.navigationModel addItem:item];
    }
    {
        YYBMainNavigationItem *item = [YYBMainNavigationItem itemWithIdentifier:@"rank"
                                                                          title:@"榜单"
                                                                           icon:[NSImage imageNamed:@"Main/Navigation/RankIcon"]
                                                                      isWebView:YES];
        item.url = @"https://testmac.yyb.qq.com/";
        [self.navigationModel addItem:item];
    }
    {
        YYBMainNavigationItem *item = [YYBMainNavigationItem itemWithIdentifier:@"software"
                                                                          title:@"软件"
                                                                           icon:[NSImage imageNamed:@"Main/Navigation/SoftwareIcon"]
                                                                       isWebView:YES];
        item.url = @"https://testmac.yyb.qq.com/";
        [self.navigationModel addItem:item];
    }
    {
        YYBMainNavigationItem *item = [YYBMainNavigationItem itemWithIdentifier:@"games"
                                                                          title:@"游戏"
                                                                           icon:[NSImage imageNamed:@"Main/Navigation/GameIcon"]
                                                                      isWebView:YES];
        item.url = @"https://testmac.yyb.qq.com/";
        [self.navigationModel addItem:item];
    }
    {
        YYBMainNavigationItem *item = [YYBMainNavigationItem itemWithIdentifier:@"User"
                                                                          title:@"我的"
                                                                           icon:[NSImage imageNamed:@"Main/Navigation/UserIcon"]
                                                                      isWebView:YES];
        item.url = @"https://devmaccclient.yyb.qq.com/mine";
        item.iconSize = NSMakeSize(28, 28);
        [self.navigationModel addItem:item];
    }
}

- (void)setupTopbarModel {
    YYBTopbarModel *topbarModel = [[YYBTopbarModel alloc] init];
    topbarModel.showBackButton = YES;
    topbarModel.showSearchField = YES;
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"refresh"
                                                                  title:@"刷新"
                                                                   icon:[NSImage imageNamed:@"Main/Top/RefreshIcon"]];
        [topbarModel addItem:item];
    }
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"localApk"
                                                                  title:@"本地安装"
                                                                   icon:[NSImage imageNamed:@"Main/Top/LocalApkIcon"]];
        [topbarModel addItem:item];
    }
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"download"
                                                                  title:@"下载"
                                                                   icon:[NSImage imageNamed:@"Main/Top/DownloadIcon"]];
        [topbarModel addItem:item];
    }
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"message"
                                                                  title:@"消息盒子"
                                                                   icon:[NSImage imageNamed:@"Main/Top/MessageIcon"]];
        [topbarModel addItem:item];
    }
    
    {
        YYBTopbarItem *item = [[YYBTopbarItem alloc] initWithIdentifier:@"setting"
                                                                  title:@"设置"
                                                                   icon:[NSImage imageNamed:@"Main/Top/SettingIcon"]];
        [topbarModel addItem:item];
    }
    
    self.topbarModel = topbarModel;
}

#pragma mark - UI Setup Methods

- (void)setupNavigationView {
    self.navigationView = [[YYBMainNavigationView alloc] initWithFrame:NSZeroRect];
    self.navigationView.delegate = self;
    self.navigationView.dataSource = self;
    [self.view addSubview:self.navigationView];
    [self.navigationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(self.view);
        self.navigationViewWidthConstraint = make.width.equalTo(@(kNavigationDefaultWidth));
    }];
}

- (void)setupContentView {
    self.contentView = [[YYBMainContentView alloc] initWithFrame:NSZeroRect];
    self.contentView.delegate = self;
    [self.view addSubview:self.contentView positioned:NSWindowBelow relativeTo:self.navigationView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.bottom.equalTo(self.view);
    }];
}

- (void)setupTopToolbarView {
    self.topToolbarView = [[YYBMainTopbarView alloc] initWithFrame:NSZeroRect];
    self.topToolbarView.delegate = self;
    self.topToolbarView.dataSource = self;
    [self.view addSubview:self.topToolbarView];
    [self.topToolbarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.equalTo(self.navigationView.mas_right);
        make.right.equalTo(self.view);
        make.height.equalTo(@(kTopBarHeight));
    }];
}


#pragma mark - YYBMainNavigationViewDataSource
- (YYBMainNavigationModel *)navigationModelForNavigationView:(YYBMainNavigationView *)navigationView {
    return self.navigationModel;
}

#pragma mark - YYBMainNavigationViewDelegate
- (void)navigationView:(YYBMainNavigationView *)navigationView didSelectItem:(YYBMainNavigationItem *)item {
    [self.contentView loadNavigationItem:item animated:NO];
}

- (void)navigationViewDidClickArrowButton:(YYBMainNavigationView *)navigationView {
    navigationView.expanded = !navigationView.isExpanded;
    
    CGFloat targetWidth = navigationView.expanded ? kNavigationExpandWidth : kNavigationDefaultWidth;
    [NSAnimationContext runAnimationGroup:^(NSAnimationContext *context) {
        context.duration = kExpandAnimationDuration;
        context.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
        [self.navigationViewWidthConstraint.animator setOffset:targetWidth];
    } completionHandler:nil];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"YYBMainNavigationViewExpandedDidChange"
                                                        object:navigationView
                                                      userInfo:@{
                                                          @"expanded": @(navigationView.expanded),
                                                          @"targetWidth": @(targetWidth)
                                                      }];
}

#pragma mark - YYBMainContentViewDelegate

- (void)contentViewDidSwitchContent:(YYBMainContentView *)contentView {
    [self.topToolbarView setBackButtonEnabled:[contentView canGoBack]];
    [self.navigationView updateSelectedItem:contentView.currentNavigationItem];
}

#pragma mark - YYBMainTopbarViewDataSource

- (YYBTopbarModel *)topbarModelForTopbarView:(YYBMainTopbarView *)topbarView {
    return self.topbarModel;
}

#pragma mark - YYBMainTopbarViewDelegate

- (void)topbarViewDidClickBackButton:(YYBMainTopbarView *)topbarView {
    [self.contentView goBack];
}

- (void)topbarView:(YYBMainTopbarView *)topbarView didClickItem:(YYBTopbarItem *)item {
    // 根据item的类型执行相应的操作
    if ([item.identifier isEqualToString:@"setting"]) {
        [self showSetingPopver:topbarView];
    }
    else if ([item.identifier isEqualToString:@"download"]) {
        [self showDownloadBoxPopver:topbarView];
    }
}

- (void)topbarView:(YYBMainTopbarView *)topbarView searchTextDidChange:(NSString *)searchText {
    YYBMacLogInfo(tag, @"Search text changed: %@", searchText);
}

#pragma mark - Popver
- (void)showSetingPopver:(NSView *)sender {
    if (self.settingPoperView && self.settingPoperView.superview) {
        [self.settingPoperView dismiss];
         self.settingPoperView = nil;
    } else {
        self.settingPoperView = [[SettingPopverView alloc] init];
        [self.settingPoperView showFromView:sender inWindow:self.view.window];
    }
}

- (void)showDownloadBoxPopver:(NSView *)sender {
    if (self.downloadBoxPoperView && self.downloadBoxPoperView.view.superview) {
        [self.downloadBoxPoperView dismiss];
         self.downloadBoxPoperView = nil;
    } else {
        self.downloadBoxPoperView = [[DownloadBoxPopver alloc] init];
        [self.downloadBoxPoperView showInView:self.view atPoint:CGPointMake(self.view.frame.size.width, self.view.frame.size.height)];
    }
}

#pragma mark - 公共导航方法

- (void)loadNavigationItem:(YYBMainNavigationItem *)item animated:(BOOL)animated {
    [self.contentView loadNavigationItem:item animated:animated];
}

@end
