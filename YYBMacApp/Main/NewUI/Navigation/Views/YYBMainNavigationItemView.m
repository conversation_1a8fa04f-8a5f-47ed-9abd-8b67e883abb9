//
//  YYBMainNavigationItemView.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/14.
//

#import "YYBMainNavigationItemView.h"
#import "Masonry.h"
#import "NSFont+YYB.h"
#import "MainUIDefine.h"
#import "YYBGlassEffectView.h"

// 定义导航项视图状态枚举
typedef NS_ENUM(NSUInteger, YYBNavigationItemViewState) {
    YYBNavigationItemViewStateNormal,   // 正常状态
    YYBNavigationItemViewStateHover,    // 鼠标悬停状态
    YYBNavigationItemViewStateSelected  // 选中状态
};

// 按钮毛玻璃效果
static NSVisualEffectMaterial const buttonStateNormal = NSVisualEffectMaterialUnderPageBackground;
static NSVisualEffectMaterial const buttonStateHover = NSVisualEffectMaterialMenu;
static NSVisualEffectMaterial const buttonStateSelected = NSVisualEffectMaterialHUDWindow;

@interface YYBMainNavigationItemView ()

@property (nonatomic, strong) NSImageView *iconImageView;
@property (nonatomic, strong) NSView *containerView;
@property (nonatomic, strong) NSView *effectView;;
@property (nonatomic, strong) NSTextField *label;
@property (nonatomic, strong) NSTrackingArea *trackingArea;
@property (nonatomic, assign) YYBNavigationItemViewState viewState;

@end

@implementation YYBMainNavigationItemView

- (instancetype)initWithItem:(YYBMainNavigationItem *)item {
    self = [self initWithFrame:NSZeroRect];
    if (self) {
        _item = item;
        [self setupViews];
        [self updateView];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setFrameSize:(NSSize)newSize {
    [super setFrameSize:newSize];
    self.label.alphaValue = (newSize.width - kNavigationIconSize) / (kNavigationExpandWidth - kNavigationDefaultWidth);
}

- (void)setupViews {
    // 设置初始状态
    self.viewState = YYBNavigationItemViewStateNormal;
    self.containerView = [[NSView alloc] init];
    self.containerView.wantsLayer = YES;
    self.containerView.layer.masksToBounds = YES;
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    // 创建容器视图
    self.effectView = [self createGlassEffectView];
    self.effectView = self.effectView ?: [self createVisualEffectView];
    self.effectView.wantsLayer = YES;
    self.effectView.layer.masksToBounds = YES;
    [self.containerView addSubview:self.effectView];
    [self.effectView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.containerView);
    }];
    
    // 创建图标视图
    NSView *iconView = [[NSView alloc] init];
    [self.containerView addSubview:iconView];
    [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self);
        make.width.mas_equalTo(iconView.mas_height);
    }];
    self.iconImageView = [[NSImageView alloc] init];
    self.iconImageView.imageScaling = NSImageScaleProportionallyUpOrDown;
    [iconView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(iconView);
        make.width.mas_equalTo(self.item.iconSize.width);
        make.height.mas_equalTo(self.item.iconSize.height);
    }];
    
    // 创建占位视图
    // 占位视图的宽度 = 文字的左边距，宽度会根据containerView的宽度做线性变化，用于文字位移动画
    CGFloat radio = (46 - 40) / (kNavigationExpandWidth - kNavigationDefaultWidth);
    CGFloat offset = 40 - radio * kNavigationDefaultWidth;
    NSView *labelLeftPaddingView = [[NSView alloc] init];
    [self.containerView addSubview:labelLeftPaddingView];
    [labelLeftPaddingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.equalTo(self.containerView);
        make.width.equalTo(self.containerView.mas_width)
            .multipliedBy(radio)
            .offset(offset);
    }];
    
    // 创建文字
    NSTextField *label = [[NSTextField alloc] initWithFrame:NSZeroRect];
    [label sizeToFit];
    label.stringValue = self.item.title;
    label.font = [NSFont PFMediumontWithsize:14];
    [label setBezeled:NO];
    [label setDrawsBackground:NO];
    [label setEditable:NO];
    [label setSelectable:NO];
    label.maximumNumberOfLines = 1;
    label.alignment = NSTextAlignmentCenter;
    [label setAlphaValue:0];
    self.label = label;
    [self.containerView addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(labelLeftPaddingView.mas_right);
        make.centerY.equalTo(self.containerView);
    }];
    
    // 添加点击事件
    NSClickGestureRecognizer *clickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleClick:)];
    [self addGestureRecognizer:clickGesture];
}

- (NSView *)createGlassEffectView {
#if defined(MAC_OS_VERSION_26_0)
    if (@available(macOS 26.0, *)) {
        NSGlassEffectView *glassEffectView = [[NSGlassEffectView alloc] init];
        glassEffectView.style = NSGlassEffectViewStyleRegular;
        glassEffectView.cornerRadius = 10;
        glassEffectView.wantsLayer = YES;
        [glassEffectView yyb_setVariant:2];
        glassEffectView.tintColor = [NSColor clearColor];
        return glassEffectView;
    }
#endif // defined(MAC_OS_VERSION_26_0)
    return nil;
}

- (NSView * )createVisualEffectView {
    NSVisualEffectView *visualEffectView = [[NSVisualEffectView alloc] init];
    visualEffectView.blendingMode = NSVisualEffectBlendingModeBehindWindow;
    visualEffectView.state = NSVisualEffectStateActive;
    visualEffectView.wantsLayer = YES;
    visualEffectView.layer.cornerRadius = 10.0;
    return visualEffectView;
}

- (void)updateTrackingAreas {
    [super updateTrackingAreas];
    
    if (self.trackingArea) {
        [self removeTrackingArea:self.trackingArea];
    }
    
    NSTrackingAreaOptions options = NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingMouseMoved;
    self.trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                     options:options
                                                       owner:self
                                                    userInfo:nil];
    [self addTrackingArea:self.trackingArea];
}

#pragma mark - 属性设置方法

- (void)setItem:(YYBMainNavigationItem *)item {
    _item = item;
    [self updateView];
}

- (void)setSelected:(BOOL)selected {
    _selected = selected;
    
    // 根据选中状态更新视图状态
    YYBNavigationItemViewState newState = selected ? YYBNavigationItemViewStateSelected : YYBNavigationItemViewStateNormal;
    [self updateViewWithState:newState];
    
    // 更新图标
    [self updateIconForSelectedState];
}

- (void)setIconSize:(CGSize)size {
    [self.iconImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(size.width);
        make.height.mas_equalTo(size.height);
    }];
}

#pragma mark - 视图更新
// 更新视图状态
- (void)updateViewWithState:(YYBNavigationItemViewState)state {
    self.viewState = state;
#if defined(MAC_OS_VERSION_26_0)
    if (@available(macOS 26.0, *)) {
        NSGlassEffectView *glassView;
        if ([self.effectView isKindOfClass:[NSGlassEffectView class]]) {
            glassView = (NSGlassEffectView *)self.effectView;
            switch (state) {
                case YYBNavigationItemViewStateNormal:
                    glassView.alphaValue = 0.0;
                    break;
                    
                case YYBNavigationItemViewStateHover:
                    glassView.alphaValue = 1.0;
                    [glassView yyb_setInteractionState:NO];
                    break;
                    
                case YYBNavigationItemViewStateSelected:
                    glassView.alphaValue = 1.0;
                    [glassView yyb_setInteractionState:YES];
                    break;
            }
            return;
        }
    }
#endif
    NSVisualEffectView *effectView;
    if ([self.effectView isKindOfClass:[NSVisualEffectView class]]) {
        effectView = (NSVisualEffectView *)self.effectView;
    }
    switch (state) {
        case YYBNavigationItemViewStateNormal:
            effectView.material = buttonStateNormal;
            break;
            
        case YYBNavigationItemViewStateHover:
            effectView.material = buttonStateHover;
            break;
            
        case YYBNavigationItemViewStateSelected:
            effectView.material = buttonStateSelected;
            break;
    }
}

- (void)updateView {
    if (!self.item) {
        return;
    }
    
    self.selected = self.item.isSelected;
    YYBNavigationItemViewState newState = self.item.isSelected ?
        YYBNavigationItemViewStateSelected : YYBNavigationItemViewStateNormal;
    [self updateViewWithState:newState];
    [self updateIconForSelectedState];
    self.hidden = !self.item.isVisible;
}

- (void)updateIconForSelectedState {
    if (self.selected && self.item.selectedIcon) {
        self.iconImageView.image = self.item.selectedIcon;
    } else {
        self.iconImageView.image = self.item.icon;
    }
}

#pragma mark - 事件处理

- (void)handleClick:(NSClickGestureRecognizer *)gesture {
    if ([self.delegate respondsToSelector:@selector(navigationItemViewDidClick:)]) {
        [self.delegate navigationItemViewDidClick:self];
    }
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];
    
    // 鼠标悬停效果
    if (!self.selected) {
        [self updateViewWithState:YYBNavigationItemViewStateHover];
    }
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];
    
    // 恢复正常效果
    if (!self.selected) {
        [self updateViewWithState:YYBNavigationItemViewStateNormal];
    }
}

@end
