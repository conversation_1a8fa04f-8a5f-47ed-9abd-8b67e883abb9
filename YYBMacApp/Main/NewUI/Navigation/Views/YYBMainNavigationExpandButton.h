//
//  YYBMainNavigationExpandButton.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/21.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

@class YYBMainNavigationExpandButton;

// 定义代理协议
@protocol YYBMainNavigationExpandButtonDelegate <NSObject>

/**
 * 当展开按钮被点击时调用
 * @param button 被点击的按钮
 */
- (void)navigationExpandButtonDidClick:(YYBMainNavigationExpandButton *)button;

@end

@interface YYBMainNavigationExpandButton : NSView

/**
 * 代理对象
 */
@property (nonatomic, weak) id<YYBMainNavigationExpandButtonDelegate> delegate;

/**
 * 设置展开按钮的图标
 * @param normalIcon 正常状态下的图标
 * @param hoverIcon 鼠标悬停状态下的图标
 */
- (void)setIcons:(NSImage *)normalIcon hoverIcon:(nullable NSImage *)hoverIcon;

/**
 * 执行箭头图标的翻转动画
 * @param expanded 是否为展开状态，决定翻转方向
 */
- (void)animateArrowWithExpanded:(BOOL)expanded;

@end

NS_ASSUME_NONNULL_END
