//
//  YYBMainNavigationExpandButton.m
//  YYBMacApp
//
//  Created by halehuang on 2025/8/21.
//

#import <QuartzCore/QuartzCore.h>
#import "YYBMainNavigationExpandButton.h"
#import "Masonry.h"
#import "MainUIDefine.h"

// 按钮状态枚举
typedef NS_ENUM(NSUInteger, YYBNavigationExpandButtonState) {
    YYBNavigationExpandButtonStateNormal = 1,   // 正常状态
    YYBNavigationExpandButtonStateHover,    // 鼠标悬停状态
    YYBNavigationExpandButtonStateSelected  // 选中状态
};

@interface YYBMainNavigationExpandButton ()

@property (nonatomic, strong) NSImageView *iconImageView;
@property (nonatomic, strong) NSView *containerView;
@property (nonatomic, strong) NSTrackingArea *trackingArea;
@property (nonatomic, assign) YYBNavigationExpandButtonState viewState;
@property (nonatomic, strong) NSImage *normalIcon;
@property (nonatomic, strong) NSImage *hoverIcon;

@end

@implementation YYBMainNavigationExpandButton

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    // 加载图标
    NSImage *icon = [NSImage imageNamed:@"Main/Navigation/ArrowLeftIcon"];
    self.normalIcon = icon;
    self.hoverIcon = icon;
    
    // 创建容器视图
    self.containerView = [[NSView alloc] init];
    self.containerView.wantsLayer = YES;
    self.containerView.layer.cornerRadius = 10.0;
    self.containerView.layer.masksToBounds = YES;
    [self addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    // 创建图标视图
    self.iconImageView = [[NSImageView alloc] init];
    self.iconImageView.imageScaling = NSImageScaleProportionallyUpOrDown;
    self.iconImageView.image = self.normalIcon;
    [self.containerView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.containerView);
        make.width.height.mas_equalTo(20.0f);
    }];
    
    // 添加点击事件
    NSClickGestureRecognizer *clickGesture = [[NSClickGestureRecognizer alloc] initWithTarget:self action:@selector(handleClick:)];
    [self addGestureRecognizer:clickGesture];
    
    // 初始化状态
    [self updateViewWithState:YYBNavigationExpandButtonStateNormal];
}

- (void)updateTrackingAreas {
    [super updateTrackingAreas];
    
    if (self.trackingArea) {
        [self removeTrackingArea:self.trackingArea];
    }
    
    NSTrackingAreaOptions options = NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingMouseMoved;
    self.trackingArea = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                     options:options
                                                       owner:self
                                                    userInfo:nil];
    [self addTrackingArea:self.trackingArea];
}

// 更新视图状态
- (void)updateViewWithState:(YYBNavigationExpandButtonState)state {
    self.viewState = state;
    
    switch (state) {
        case YYBNavigationExpandButtonStateNormal:
            self.containerView.layer.backgroundColor = [NSColor clearColor].CGColor;
            self.iconImageView.image = self.normalIcon;
            break;
            
        case YYBNavigationExpandButtonStateHover:
            self.containerView.layer.backgroundColor = [[NSColor whiteColor] colorWithAlphaComponent:0.08].CGColor;
            self.iconImageView.image = self.hoverIcon ?: self.normalIcon;
            break;
            
        case YYBNavigationExpandButtonStateSelected:
            self.containerView.layer.backgroundColor = [NSColor clearColor].CGColor;
            self.iconImageView.image = self.hoverIcon ?: self.normalIcon;
            break;
    }
}

#pragma mark - 事件处理

- (void)handleClick:(NSClickGestureRecognizer *)gesture {
    // 通过代理通知点击事件
    if ([self.delegate respondsToSelector:@selector(navigationExpandButtonDidClick:)]) {
        [self.delegate navigationExpandButtonDidClick:self];
    }
}

#pragma mark - 公共方法

- (void)setIcons:(NSImage *)normalIcon hoverIcon:(nullable NSImage *)hoverIcon {
    self.normalIcon = normalIcon;
    self.hoverIcon = hoverIcon;
    
    // 根据当前状态更新图标
    if (self.viewState == YYBNavigationExpandButtonStateNormal) {
        self.iconImageView.image = normalIcon;
    } else {
        self.iconImageView.image = hoverIcon ?: normalIcon;
    }
}

#pragma mark - Animation
- (void)setAnchorPoint:(CGPoint)anchorPoint forView:(NSView *)view {
    CALayer *layer = view.layer;
    CGPoint oldAnchor = layer.anchorPoint;
    CGPoint oldPosition = layer.position;
    CGSize boundsSize = layer.bounds.size;
    
    // 设置新 anchorPoint
    layer.anchorPoint = anchorPoint;
    
    // 计算新的 position，确保视图位置不变
    CGPoint newPosition;
    newPosition.x = oldPosition.x + (anchorPoint.x - oldAnchor.x) * boundsSize.width;
    newPosition.y = oldPosition.y + (anchorPoint.y - oldAnchor.y) * boundsSize.height;
    layer.position = newPosition;
}

- (void)animateArrowWithExpanded:(BOOL)expanded {
    [self setAnchorPoint:CGPointMake(0.5, 0.5) forView:self.iconImageView];

    CABasicAnimation *flipAnimation = [CABasicAnimation animationWithKeyPath:@"transform.scale.x"];
    
    CGFloat startScale = expanded ? 1 : -1;
    CGFloat endScale = expanded ? -1 : 1;
    
    flipAnimation.fromValue = @(startScale);
    flipAnimation.toValue = @(endScale);
    flipAnimation.duration = kExpandAnimationDuration;
    flipAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kExpandAnimationTimingFunction];
    flipAnimation.removedOnCompletion = NO;
    flipAnimation.fillMode = kCAFillModeForwards;
    
    [self.iconImageView.layer addAnimation:flipAnimation forKey:@"flipAnimation"];
}

- (void)mouseEntered:(NSEvent *)event {
    [super mouseEntered:event];
    
    // 鼠标悬停效果
    [self updateViewWithState:YYBNavigationExpandButtonStateHover];
}

- (void)mouseExited:(NSEvent *)event {
    [super mouseExited:event];
    
    // 恢复正常效果
    [self updateViewWithState:YYBNavigationExpandButtonStateNormal];
}

@end
