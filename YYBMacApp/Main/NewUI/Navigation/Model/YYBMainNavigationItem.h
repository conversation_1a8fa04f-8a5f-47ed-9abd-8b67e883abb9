//
//  YYBMainNavigationItem.h
//  YYBMacApp
//
//  Created by CodeBuddy on 2025/8/14.
//

#import <Foundation/Foundation.h>
#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, YYBNavigationItemType) {
    YYBNavigationItemTypeNormal,    // 普通导航项
    YYBNavigationItemTypeSelected,  // 选中状态的导航项
};

@interface YYBMainNavigationItem : NSObject

// 导航项标识符
@property (nonatomic, copy) NSString *identifier;

// 导航项图标
@property (nonatomic, strong, nullable) NSImage *icon;

// 导航项标题
@property (nonatomic, strong, nullable) NSString *title;

// 导航项选中状态图标
@property (nonatomic, strong, nullable) NSImage *selectedIcon;

// 导航项选中状态图标，默认20
@property (nonatomic, assign) NSSize iconSize;

// 是否用WebView展示
@property (nonatomic, assign) BOOL isWebView;

// 导航项类型
@property (nonatomic, assign) YYBNavigationItemType type;

// 导航项是否被选中
@property (nonatomic, assign) BOOL isSelected;

// 导航项是否可见
@property (nonatomic, assign) BOOL isVisible;

// 网页url，isWebView为YES时有效
@property (nonatomic, strong, nullable) NSString *url;

// 自定义内容视图，当isWebView为NO时可以使用
@property (nonatomic, strong, nullable) NSView *contentView;

// 便捷初始化方法
+ (instancetype)itemWithIdentifier:(NSString *)identifier
                             title:(nullable NSString *)title
                              icon:(nullable NSImage *)icon
                         isWebView:(BOOL)isWebView;

+ (instancetype)itemWithIdentifier:(NSString *)identifier
                            title:(nullable NSString *)title
                             icon:(nullable NSImage *)icon
                     selectedIcon:(nullable NSImage *)selectedIcon
                        isWebView:(BOOL)isWebView;

@end

NS_ASSUME_NONNULL_END
