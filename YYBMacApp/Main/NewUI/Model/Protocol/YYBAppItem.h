#import <Foundation/Foundation.h>
#import "YYBBaseModel.h"
#import "YYBReportInfo.h"
#import "YYBAppItem.h"
#import "YYBBgColor.h"
#import "YYBBookingInfo.h"
#import "YYBContent.h"
#import "YYBExtInfo.h"
#import "YYBPcGameInfo.h"
#import "YYBReportInfo.h"
#import "YYBTagRankInfo.h"
#import "YYBWxRankInfo.h"
#import "YYBSpecialTag.h"
#import "YYBRankingItem.h"
#import "YYBForceTags.h"

NS_ASSUME_NONNULL_BEGIN
@interface YYBAppItem : YYBBaseModel
@property (nonatomic, copy, nullable) NSString *adPosId;
@property (nonatomic, strong, nullable) NSNumber *adapted;
@property (nonatomic, copy, nullable) NSString *apkUrl;
@property (nonatomic, strong, nullable) NSNumber *appArchAdapted;
@property (nonatomic, strong, nullable) NSNumber *appArchInstallable;
@property (nonatomic, copy, nullable) NSString *appGrayId;
@property (nonatomic, strong, nullable) NSNumber *appId;
@property (nonatomic, copy, nullable) NSString *appReleaseTime;
@property (nonatomic, strong, nullable) NSNumber *appPublishTime;
@property (nonatomic, copy, nullable) NSString *appSrc;
@property (nonatomic, strong, nullable) NSNumber *appUpdateTime;
@property (nonatomic, strong, nullable) NSNumber *begTime;
@property (nonatomic, copy, nullable) NSString *bookingCount;
@property (nonatomic, strong, nullable) NSNumber *cateId;
@property (nonatomic, strong, nullable) NSNumber *cateIdNew;
@property (nonatomic, copy, nullable) NSString *cateNameNew;
@property (nonatomic, copy, nullable) NSString *channelId;
@property (nonatomic, copy, nullable) NSString *channelName;
@property (nonatomic, strong, nullable) NSNumber *checkLevel;
@property (nonatomic, strong, nullable) NSNumber *chipArchAdapted;
@property (nonatomic, copy, nullable) NSString *cid;
@property (nonatomic, strong, nullable) NSNumber *clientType;
@property (nonatomic, copy, nullable) NSString *cloudIcon;
@property (nonatomic, copy, nullable) NSString *cooperateType;
@property (nonatomic, copy, nullable) NSString *ctype;
@property (nonatomic, copy, nullable) NSString *developer;
@property (nonatomic, copy, nullable) NSString *directCondition;
@property (nonatomic, copy, nullable) NSString *editorIntro;
@property (nonatomic, strong, nullable) NSNumber *enableQqLogin;
@property (nonatomic, strong, nullable) NSNumber *enableWxLogin;
@property (nonatomic, strong, nullable) NSNumber *endTime;
@property (nonatomic, copy, nullable) NSString *engineName;
@property (nonatomic, strong, nullable) NSNumber *entityId;
@property (nonatomic, copy, nullable) NSString *expIds;
@property (nonatomic, strong, nullable) NSNumber *experienceLevel;
@property (nonatomic, copy, nullable) NSString *fTags;
@property (nonatomic, copy, nullable) NSString *feature;
@property (nonatomic, strong, nullable) NSNumber *finalize;
@property (nonatomic, strong, nullable) NSNumber *finalizeCl;
@property (nonatomic, copy, nullable) NSString *finalizeReason;
@property (nonatomic, copy, nullable) NSString *forceType;
@property (nonatomic, strong, nullable) NSNumber *forceUpdate;
@property (nonatomic, copy, nullable) NSString *gameId;
@property (nonatomic, strong, nullable) NSNumber *gameType;
@property (nonatomic, copy, nullable) NSString *horizontalCardBgColor;
@property (nonatomic, copy, nullable) NSString *ichannel;
@property (nonatomic, copy, nullable) NSString *iconColor;
@property (nonatomic, copy, nullable) NSString *icpNumber;
@property (nonatomic, strong, nullable) NSNumber *id;
@property (nonatomic, strong, nullable) NSNumber *ignoreInstalledVersion;
@property (nonatomic, strong, nullable) NSNumber *isBooking;
@property (nonatomic, strong, nullable) NSNumber *isOfficial;
@property (nonatomic, copy, nullable) NSString *keyword;
@property (nonatomic, copy, nullable) NSString *mTags;
@property (nonatomic, strong, nullable) NSNumber *materialId;
@property (nonatomic, copy, nullable) NSString *md5;
@property (nonatomic, strong, nullable) NSNumber *mid;
@property (nonatomic, strong, nullable) NSNumber *online;
@property (nonatomic, copy, nullable) NSString *onlineReason;
@property (nonatomic, strong, nullable) NSNumber *onlyUpdate;
@property (nonatomic, strong, nullable) NSNumber *openPlatformStatus;
@property (nonatomic, strong, nullable) NSNumber *parallelVision;
@property (nonatomic, strong, nullable) NSNumber *parentCateId;
@property (nonatomic, strong, nullable) NSNumber *payType;
@property (nonatomic, strong, nullable) NSNumber *payTypeSwitch;
@property (nonatomic, copy, nullable) NSString *pkgAlias;
@property (nonatomic, copy, nullable) NSString *pkgName;
@property (nonatomic, strong, nullable) NSNumber *platform;
@property (nonatomic, strong, nullable) NSNumber *pos;
@property (nonatomic, copy, nullable) NSString *privacyAgreement;
@property (nonatomic, copy, nullable) NSString *qqAppId;
@property (nonatomic, strong, nullable) NSNumber *rank_1;
@property (nonatomic, strong, nullable) NSNumber *rank_2;
@property (nonatomic, strong, nullable) NSNumber *rank_3;
@property (nonatomic, strong, nullable) NSNumber *rank_4;
@property (nonatomic, strong, nullable) NSNumber *rank_5;
@property (nonatomic, copy, nullable) NSString *rankAll;
@property (nonatomic, strong, nullable) NSNumber *rankCtr;
@property (nonatomic, strong, nullable) NSNumber *rankCvr;
@property (nonatomic, strong, nullable) NSNumber *rankDau;
@property (nonatomic, strong, nullable) NSNumber *rankDuration;
@property (nonatomic, strong, nullable) NSNumber *rankPv;
@property (nonatomic, strong, nullable) NSNumber *rankRemain;
@property (nonatomic, strong, nullable) NSNumber *rankScore;
@property (nonatomic, copy, nullable) NSString *ratingInfo;
@property (nonatomic, copy, nullable) NSString *ratingInfoAverageRating;
@property (nonatomic, strong, nullable) NSNumber *ratingInfoRatingCount;
@property (nonatomic, copy, nullable) NSString *recmdType;
@property (nonatomic, copy, nullable) NSString *relevanceInfo;
@property (nonatomic, strong, nullable) NSNumber *resPos;
@property (nonatomic, copy, nullable) NSString *searchOption;
@property (nonatomic, strong, nullable) NSNumber *silentUpdate;
@property (nonatomic, strong, nullable) NSNumber *size;
@property (nonatomic, strong, nullable) NSNumber *sourceId;
@property (nonatomic, copy, nullable) NSString *sourceType;
@property (nonatomic, copy, nullable) NSString *spec;
@property (nonatomic, strong, nullable) NSNumber *status;
@property (nonatomic, strong, nullable) NSNumber *suitableAge;
@property (nonatomic, copy, nullable) NSString *syzsIcon;
@property (nonatomic, strong, nullable) NSNumber *syzsStatus;
@property (nonatomic, strong, nullable) NSNumber *tagId;
@property (nonatomic, strong, nullable) NSNumber *tagIdIdx;
@property (nonatomic, copy, nullable) NSString *traceId;
@property (nonatomic, copy, nullable) NSString *type;
@property (nonatomic, strong, nullable) NSNumber *upGameRank7days;
@property (nonatomic, strong, nullable) NSNumber *updateTime;
@property (nonatomic, strong, nullable) NSNumber *versionCode;
@property (nonatomic, copy, nullable) NSString *versionName;
@property (nonatomic, copy, nullable) NSString *videoUrl;
@property (nonatomic, copy, nullable) NSString *windowsIcon;
@property (nonatomic, copy, nullable) NSString *wxAppId;
@property (nonatomic, copy, nullable) NSString *wxGhId;
@property (nonatomic, copy, nullable) NSString *yybAdInfo;
@property (nonatomic, copy, nullable) NSString *yybMixerAdInfo;
@property (nonatomic, copy, nullable) NSString *yybWxMiniGameAdInfoV2;


@property (nonatomic, strong, nullable) YYBBookingInfo *bookingInfo;
@property (nonatomic, strong, nullable) YYBContent *content;
@property (nonatomic, strong, nullable) YYBExtInfo *extInfo;
@property (nonatomic, strong, nullable) YYBPcGameInfo *pcGameInfo;
@property (nonatomic, strong, nullable) YYBReportInfo *reportInfo;
@property (nonatomic, strong, nullable) YYBTagRankInfo *tagRankInfo;
@property (nonatomic, strong, nullable) YYBWxRankInfo *wxRankInfo;

@property (nonatomic, copy, nullable) NSArray<NSString *> *auditedSnapshots;
@property (nonatomic, copy, nullable) NSArray<NSString *> *auditedVideoUrl;
@property (nonatomic, copy, nullable) NSArray<NSString *> *horizontalBreakWindow;
@property (nonatomic, copy, nullable) NSArray<NSString *> *verticalBreakWindow;
@property (nonatomic, copy, nullable) NSArray<NSString *> *verticalCover;
@property (nonatomic, copy, nullable) NSArray<NSNumber *> *gameTagId;
@property (nonatomic, copy, nullable) NSArray<NSString *> *gameTagName;
@property (nonatomic, copy, nullable) NSArray<NSString *> *showTagName;
@property (nonatomic, copy, nullable) NSArray<NSString *> *tagName;
@property (nonatomic, copy, nullable) NSArray<YYBSpecialTag *> *specialTagInfo;
@property (nonatomic, copy, nullable) NSArray<YYBForceTags *> *forceTagsInfo;
@property (nonatomic, copy, nullable) NSArray<YYBRankingItem *> *rankingItemsRaw;
@property (nonatomic, copy, nullable) NSArray<NSString *> *wxGameTags;

@property (nonatomic, copy, nullable) NSDictionary *extra;

@property (nonatomic, copy, nullable) NSString *icon;              // icon图
@property (nonatomic, copy, nullable) NSString *name;              // app名称
@property (nonatomic, copy, nullable) NSString *cateName;          // 分类
@property (nonatomic, copy, nullable) NSString *desc;              // 简介
@property (nonatomic, copy, nullable) NSString *subTitle;          // 副标题/榜单下小字副标题

@property (nonatomic, strong, nullable) NSNumber *rank;            // 榜单排名
@property (nonatomic, strong, nullable) NSNumber *downloadNum;     // 累计下载量
@property (nonatomic, strong, nullable) NSNumber *weekDownloadNum; // 本周下载量

@property (nonatomic, copy, nullable) NSString *editorScore;       // 编辑评分
@property (nonatomic, copy, nullable) NSString *specialTagName;    // 如“支持iOS、大神键位”等
@property (nonatomic, copy, nullable) NSString *version;           // 版本名
@property (nonatomic, copy, nullable) NSString *author;            // 开发商
@property (nonatomic, strong, nullable) YYBBgColor *bgColor;;      // 榜单等背景色

@property (nonatomic, strong, nullable) NSArray<NSString *> *horizontalCover;      // Banner大图
@property (nonatomic, strong, nullable) NSArray<NSString *> *snapshots;            // 更多截图

@property (nonatomic, copy, nullable) NSString *jumpUrl;           // 跳转下载/详情
@property (nonatomic, copy, nullable) NSString *periodTag;         // 如[新上架]/[飙升]
@property (nonatomic, copy, nullable) NSString *tips;              // 特殊tips标签

@property (nonatomic, strong, nullable) NSNumber *isNew;           // 是否新品
@property (nonatomic, strong, nullable) NSNumber *isHot;           // 是否热门

@property (nonatomic, copy, nullable) NSString *editorInfo;         // 一句话简介

@end
NS_ASSUME_NONNULL_END

