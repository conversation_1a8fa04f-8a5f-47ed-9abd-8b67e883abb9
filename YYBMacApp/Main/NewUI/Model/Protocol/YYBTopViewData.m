//
//  YYBTopViewData.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/22.
//

#import "YYBTopViewData.h"

@interface YYBTopViewData ()
@property (nonatomic, strong) NSString *horizontalCardBgColor; // 封面横图卡背景颜色
@property (nonatomic, strong) NSArray<NSString *> *snapshotsCoverImages; // 封面横图 + 五图

@end

@implementation YYBTopViewData
- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    YYBAppItem *appItem = self.itemData.firstObject;
    if (!appItem) {
        return NO;
    }
    NSMutableArray *images = [NSMutableArray array];
    if (appItem.horizontalCover) {
        [images addObjectsFromArray:appItem.horizontalCover];
    }
    if (appItem.snapshots) {
        [images addObjectsFromArray:appItem.snapshots];
    }
    self.snapshotsCoverImages = images.copy;
    return YES;
}

@end
