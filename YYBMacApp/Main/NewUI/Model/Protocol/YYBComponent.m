#import "YYBComponent.h"
#import "YYModel.h"
#import "YYBComponent.h"
#import "YYBGridCardData.h"
#import "YYBResponsiveCardData.h"
#import "YYBRankFilterData.h"
#import "YYBTodayActivityCardData.h"
#import "YYBDynamicLunboCardV3CardData.h"
#import "YYBApplicationCardsCardData.h"
#import "YYBRankCardData.h"
#import "YYBHotGameCardData.h"
#import "YYBHotAppContentListCardData.h"
#import "YYBRecommendCardData.h"
#import "YYBTopViewData.h"

@implementation YYBComponent
- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    id data = dic[@"data"];
    if (!data || data == (id)kCFNull) { return YES; }
    if ([data isKindOfClass:[NSDictionary class]]) {
        if ([self.cardId isEqualToString:@"GridCard"]) {
            self.data = [YYBGridCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"ResponsiveCard"]) {
            self.data = [YYBResponsiveCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"PopularAppRankFilter"] || [self.cardId isEqualToString:@"GameRankFilter"]) {
            self.data = [YYBRankFilterData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"TodayActivity"]) {
            self.data = [YYBTodayActivityCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"DynamicLunboCardV3"]) {
            self.data = [YYBDynamicLunboCardV3CardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"ApplicationCards"]) {
            self.data = [YYBApplicationCardsCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"PopularAppRank"] || [self.cardId isEqualToString:@"GameRank"] || [self.cardId isEqualToString:@"WxGameRank"]) {
            self.data = [YYBRankCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"HotGame"]) {
            self.data = [YYBHotGameCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"HotAppContentListCard"]) {
            self.data = [YYBHotAppContentListCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"RecommendCard"]) {
            self.data = [YYBRecommendCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"RecomTopView"]) {
            self.data = [YYBTopViewData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"RankLeft"]) {
            self.data = [YYBRankCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"RankMidTop"]) {
            self.data = [YYBRankCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"RankMidBot"]) {
            self.data = [YYBRankCardData yy_modelWithDictionary:data];
        } else if ([self.cardId isEqualToString:@"RankRight"]) {
            self.data = [YYBRankCardData yy_modelWithDictionary:data];
        } else {
            self.data = [YYBBaseCardData yy_modelWithDictionary:data];
        }
    }
    return YES;
}
@end

