#import "YYBAppItem.h"

@implementation YYBAppItem
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"specialTagInfo": [YYBSpecialTag class],
        @"rankingItemsRaw": [YYBRankingItem class],
        @"forceTagsInfo": [YYBForceTags class],
    };
}

- (BOOL)modelCustomTransformFromDictionary:(NSDictionary *)dic {
    NSDictionary *itemData = dic[@"itemData"];
    if ([itemData isKindOfClass:NSDictionary.class]) {
        NSString *snapshots = itemData[@"snapshots"];
        if ([snapshots isKindOfClass:NSString.class]) {
            self.snapshots = [[snapshots stringByReplacingOccurrencesOfString:@" " withString:@""] componentsSeparatedByString:@","];
        }
    }
    return YES;
}

@end

