//
//  YYBRankCardData.h
//  YYBMacBusinessComponents
//
//  Created by halehuang on 2025/8/21.
//

#import "YYBBaseCardData.h"

NS_ASSUME_NONNULL_BEGIN

@interface YYBRankCardData : YYBBaseCardData

@property (nonatomic, copy, nullable) NSString *title;
@property (nonatomic, strong, nullable) NSNumber *moreLinkTagId;
@property (nonatomic, copy, nullable) NSString *moreLinkRoutePath;

/// 榜单子标题，“本周新增下载榜/累计下载榜”等
@property (nonatomic, copy, nullable) NSString *desc;

/// 卡片右上角的榜单周期说明（如 “本周下载排行”）
@property (nonatomic, copy, nullable) NSString *periodLabel;

/// 榜单专属icon/banner
@property (nonatomic, copy, nullable) NSString *banner;

/// 榜单类型，如“热门应用”“飙升应用”
@property (nonatomic, copy, nullable) NSString *rankType;

/// 榜单的展示周期，如“周榜”“月榜”，便于切换扩展
@property (nonatomic, copy, nullable) NSString *rankPeriod;

/// 榜单顶部分组名（如 游戏/应用/飙升游戏/飙升应用），用于UI Tab切换
@property (nonatomic, copy, nullable) NSString *sectionName;

/// 榜单专属色值，如UI底色，如果数据源有则对接
@property (nonatomic, copy, nullable) NSString *bgColor;

/// 榜单底部的备注提示，如“数据来源...”
/// 比如可用于“仅展示部分热门TOP10应用”说明
@property (nonatomic, copy, nullable) NSString *footerTip;

@end

NS_ASSUME_NONNULL_END
