//
//  ThumbnailControlView.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/22.
//

#import <Cocoa/Cocoa.h>
#import "BannerItem.h"

NS_ASSUME_NONNULL_BEGIN

@class ThumbnailControlView;

@protocol ThumbnailControlDelegate <NSObject>
@optional
- (void)thumbnailControlView:(ThumbnailControlView *)controlView didSelectIndex:(NSInteger)index;
- (void)thumbnailControlView:(ThumbnailControlView *)controlView didHoverIndex:(NSInteger)index;
@end

@interface ThumbnailControlView : NSView

@property (nonatomic, weak) id<ThumbnailControlDelegate> delegate;
@property (nonatomic, assign) NSInteger selectedIndex;

- (void)setThumbnailItems:(NSArray<BannerItem *> *)items;
- (void)setSelectedIndex:(NSInteger)index;

@end

NS_ASSUME_NONNULL_END
