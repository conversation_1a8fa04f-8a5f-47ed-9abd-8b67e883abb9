//
//  YYBTopView.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/19.
//

#import <Cocoa/Cocoa.h>
@class YYBComponent;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, YYBTopViewType) {
    TopViewNormalType, // 常规类型，视频/图片 与视频左右显示
    TopViewBigVideo,   // 视频撑满窗口
};

@interface YYBTopView : NSView

- (void)configureWithComponent:(YYBComponent *)component;

/// 设置内容边距
- (void)setEdgeInsets:(NSEdgeInsets)edgeInsets;
@end

NS_ASSUME_NONNULL_END
