//
//  TopViewBannerView.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/21.
//

#import "TopViewBannerView.h"
#import "YYBVideoManager.h"
#import "YYBTPVideo.h"
#import "Masonry.h"
#import "ThumbnailControlView.h"
#import "ContentDisplayView.h"
#import "YYBComponent.h"

@interface TopViewBannerView () <NSCollectionViewDelegate, NSCollectionViewDataSource, ThumbnailControlDelegate>
@property (nonatomic, strong) NSArray<BannerItem *> *bannerItems;
@property (nonatomic, assign) BOOL autoPlayEnabled;
@property (nonatomic, assign) NSTimeInterval imageDisplayDuration;


@property (nonatomic, strong) NSCollectionView *collectionView;
@property (nonatomic, strong) NSCollectionViewFlowLayout *flowLayout;
@property (nonatomic, strong) ThumbnailControlView *thumbnailControlView;
@property (nonatomic, strong) NSTimer *autoPlayTimer;
@property (nonatomic, assign) NSInteger currentIndex;
@property (nonatomic, assign) BOOL isAutoPlaying;
@property (nonatomic, strong) NSButton *previousButton;
@property (nonatomic, strong) NSButton *nextButton;
@end

@implementation TopViewBannerView

- (instancetype)initWithBannerItems:(NSArray<BannerItem *> *)items {
    self = [super init];
    if (self) {
        _bannerItems = items;
        _autoPlayEnabled = YES;
        _imageDisplayDuration = 6.0;
        _currentIndex = 0;
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    self.wantsLayer = YES;
    self.layer.backgroundColor = NSColor.blackColor.CGColor;
    [self setupCollectionView];
    [self setupThumbnailControlView];
    if (self.autoPlayEnabled) {
        [self startAutoPlay];
    }
}

- (void)setupImageGuide {
    // 上一张按钮
    self.previousButton = [[NSButton alloc] init];
    [self.previousButton setTitle:@"‹"];
    [self.previousButton setBezelStyle:NSBezelStyleRounded];
    [self.previousButton setFont:[NSFont systemFontOfSize:24 weight:NSFontWeightBold]];
    [self.previousButton setTarget:self];
    [self.previousButton setAction:@selector(previousImage)];
    [self addSubview:self.previousButton];
    
    // 下一张按钮
    self.nextButton = [[NSButton alloc] init];
    [self.nextButton setTitle:@"›"];
    [self.nextButton setBezelStyle:NSBezelStyleRounded];
    [self.nextButton setFont:[NSFont systemFontOfSize:24 weight:NSFontWeightBold]];
    [self.nextButton setTarget:self];
    [self.nextButton setAction:@selector(nextImage)];
    [self addSubview:self.nextButton];
}

- (void)setupCollectionView {
    self.flowLayout = [[NSCollectionViewFlowLayout alloc] init];
    self.flowLayout.minimumLineSpacing = 0;
    self.flowLayout.minimumInteritemSpacing = 0;
    self.flowLayout.scrollDirection = NSCollectionViewScrollDirectionHorizontal;
    
    self.collectionView = [[NSCollectionView alloc] init];
    self.collectionView.collectionViewLayout = self.flowLayout;
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.allowsEmptySelection = YES;
    self.collectionView.allowsMultipleSelection = NO;
    self.collectionView.selectable = YES;
    
    [self.collectionView registerClass:[ContentDisplayView class] forItemWithIdentifier:NSStringFromClass(ContentDisplayView.class)];
    
    [self addSubview:self.collectionView];
    
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

- (void)setupThumbnailControlView {
    self.thumbnailControlView = [[ThumbnailControlView alloc] init];
    self.thumbnailControlView.delegate = self;
    [self addSubview:self.thumbnailControlView];
    
    [self.thumbnailControlView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(48);
        make.bottom.equalTo(self).offset(-40);
        make.width.mas_equalTo(200);
        make.height.mas_equalTo(60);
    }];
}

- (void)configureWithComponent:(YYBComponent *)component {
    
}

#pragma mark - NSCollectionViewDataSource
- (NSInteger)collectionView:(NSCollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.bannerItems.count;
}

- (nonnull NSCollectionViewItem *)collectionView:(nonnull NSCollectionView *)collectionView itemForRepresentedObjectAtIndexPath:(nonnull NSIndexPath *)indexPath { 
    ContentDisplayView *contentView = [collectionView makeItemWithIdentifier:NSStringFromClass(ContentDisplayView.class) forIndexPath:indexPath];
    
    BannerItem *bannerItem = self.bannerItems[indexPath.item];
    [contentView displayItem:bannerItem];
    return contentView;
}

#pragma mark - NSCollectionViewDelegateFlowLayout

- (NSSize)collectionView:(NSCollectionView *)collectionView layout:(NSCollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    return self.bounds.size;
}

#pragma mark - 图片切换
- (void)previousImage {
    
}

- (void)nextImage {
    
}

#pragma mark - 轮播控制

- (void)startAutoPlay {
    self.isAutoPlaying = YES;
    self.currentIndex = 0;
    [self scrollToIndex:0 animated:NO];
    [self scheduleNextAutoPlay];
}

- (void)scheduleNextAutoPlay {
    [self.autoPlayTimer invalidate];
    self.autoPlayTimer = nil;
    
    if (!self.isAutoPlaying) return;
    
    self.autoPlayTimer = [NSTimer scheduledTimerWithTimeInterval:0.1 repeats:YES block:^(NSTimer * _Nonnull timer) {
        [self checkAutoPlayProgress];
    }];
}

- (void)checkAutoPlayProgress {
    if (self.currentIndex < self.bannerItems.count) {
        // 图片展示6秒
        static NSTimeInterval imageStartTime = 0;
        static BOOL isTiming = NO;
        
        if (!isTiming) {
            imageStartTime = [[NSDate date] timeIntervalSince1970];
            isTiming = YES;
        }
        
        NSTimeInterval elapsed = [[NSDate date] timeIntervalSince1970] - imageStartTime;
        if (elapsed >= 6.0) {
            isTiming = NO;
            [self moveToNextItem];
        }
    } else {
        // 视频播放
        [self checkVideoPlayback];
    }
}

- (void)checkVideoPlayback {
    // 检查视频播放状态
  
}

- (void)moveToNextItem {
    NSInteger totalItems = self.bannerItems.count + 1;
    NSInteger nextIndex = (self.currentIndex + 1) % totalItems;
    [self scrollToIndex:nextIndex animated:YES];
}

- (void)scrollToIndex:(NSInteger)index animated:(BOOL)animated {
    if (index < 0 || index >= [self collectionView:self.collectionView numberOfItemsInSection:0]) {
        return;
    }
    
    self.currentIndex = index;
    
    NSIndexPath *indexPath = [NSIndexPath indexPathForItem:index inSection:0];
    [[self.collectionView animator] scrollToItemsAtIndexPaths:[NSSet setWithObject:indexPath]
                                               scrollPosition:NSCollectionViewScrollPositionCenteredHorizontally];
    
    // 更新缩略图状态
    [self.thumbnailControlView setSelectedIndex:index];
    
    // 处理视频播放
}


#pragma mark - ThumbnailControlViewDelegate
- (void)thumbnailControlView:(ThumbnailControlView *)controlView didSelectIndex:(NSInteger)index {
    [self scrollToIndex:index animated:YES];
}

- (void)thumbnailControlView:(ThumbnailControlView *)controlView didHoverIndex:(NSInteger)index {
    if (index == self.bannerItems.count) {
        // hover视频缩略图，立即播放视频
        [self scrollToIndex:index animated:YES];
    } else {
        // hover图集缩略图，显示图片
        [self scrollToIndex:index animated:YES];
    }
}

#pragma mark - 清理

- (void)dealloc {
    [self.autoPlayTimer invalidate];
    
//    [self stopCurrentVideo];
}


- (void)showNextItem {
}

- (void)showPreviousItem {
}

- (void)stopAutoPlay {
}


- (void)showItemAtIndex:(NSInteger)index {
}

@end
