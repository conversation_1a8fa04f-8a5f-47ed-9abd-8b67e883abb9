//
//  ThumbnailControlView.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/22.
//

#import "ThumbnailControlView.h"
#import "Masonry.h"

@interface ThumbnailControlView ()
@property (nonatomic, strong) NSButton *videoThumbnailButton;
@property (nonatomic, strong) NSButton *imageThumbnailButton;
@property (nonatomic, strong) NSImage *videoThumbnailImage;
@property (nonatomic, strong) NSImage *imageThumbnailImage;
@end

@implementation ThumbnailControlView

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    self.wantsLayer = YES;
    self.layer.backgroundColor = [NSColor colorWithWhite:0.1 alpha:0.8].CGColor;
    self.layer.cornerRadius = 8;
    
    // 视频缩略图按钮
    self.videoThumbnailButton = [[NSButton alloc] init];
    [self.videoThumbnailButton setImageScaling:NSImageScaleAxesIndependently];
    [self.videoThumbnailButton setBezelStyle:NSBezelStyleRegularSquare];
    [self.videoThumbnailButton setBordered:NO];
    [self.videoThumbnailButton setTarget:self];
    [self.videoThumbnailButton setAction:@selector(videoThumbnailClicked:)];
    [self addSubview:self.videoThumbnailButton];
    
    // 图集缩略图按钮
    self.imageThumbnailButton = [[NSButton alloc] init];
    [self.imageThumbnailButton setImageScaling:NSImageScaleAxesIndependently];
    [self.imageThumbnailButton setBezelStyle:NSBezelStyleRegularSquare];
    [self.imageThumbnailButton setBordered:NO];
    [self.imageThumbnailButton setTarget:self];
    [self.imageThumbnailButton setAction:@selector(imageThumbnailClicked:)];
    [self addSubview:self.imageThumbnailButton];
    

    // 添加hover事件
    NSTrackingArea *videoTrackingArea = [[NSTrackingArea alloc] initWithRect:self.videoThumbnailButton.bounds
                                                                     options:NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow
                                                                       owner:self
                                                                    userInfo:@{@"button": @"video"}];
    [self.videoThumbnailButton addTrackingArea:videoTrackingArea];
    
    NSTrackingArea *imageTrackingArea = [[NSTrackingArea alloc] initWithRect:self.imageThumbnailButton.bounds
                                                                     options:NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow
                                                                       owner:self
                                                                    userInfo:@{@"button": @"image"}];
    [self.imageThumbnailButton addTrackingArea:imageTrackingArea];
    
    // 约束
    [self setupConstraints];
    
    // 默认选中第一个
    self.selectedIndex = 0;
    [self updateSelectionStates];
}

- (void)setupConstraints {
    [self.videoThumbnailButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(10);
        make.centerY.equalTo(self);
        make.width.height.mas_equalTo(40);
    }];
    
    [self.imageThumbnailButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.videoThumbnailButton.mas_right).offset(10);
        make.centerY.equalTo(self);
        make.width.height.mas_equalTo(40);
        make.right.equalTo(self).offset(-10);
    }];
}

- (void)updateSelectionForIndex:(NSInteger)index {
    self.selectedIndex = index;
    [self updateSelectionStates];
}

- (void)updateSelectionStates {
    BOOL isVideoSelected = (self.selectedIndex == 1); // 假设视频是第2个（索引1）
    
    if (isVideoSelected) {
        self.videoThumbnailButton.layer.borderColor = [NSColor systemBlueColor].CGColor;
        self.videoThumbnailButton.layer.borderWidth = 2;
        self.imageThumbnailButton.layer.borderWidth = 0;
    } else {
        self.imageThumbnailButton.layer.borderColor = [NSColor systemBlueColor].CGColor;
        self.imageThumbnailButton.layer.borderWidth = 2;
        self.videoThumbnailButton.layer.borderWidth = 0;
    }
}

#pragma mark - 事件处理

- (void)videoThumbnailClicked:(NSButton *)sender {
    if ([self.delegate respondsToSelector:@selector(thumbnailControlView:didSelectIndex:)]) {
        [self.delegate thumbnailControlView:self didSelectIndex:1];
    }
}

- (void)imageThumbnailClicked:(NSButton *)sender {
    if ([self.delegate respondsToSelector:@selector(thumbnailControlView:didSelectIndex:)]) {
        [self.delegate thumbnailControlView:self didSelectIndex:0]; // 图片索引
    }
}

#pragma mark - 鼠标事件

- (void)mouseEntered:(NSEvent *)event {
    NSDictionary *userInfo = [event userData];
    NSString *buttonType = userInfo[@"button"];
    
    if ([buttonType isEqualToString:@"video"]) {
        if ([self.delegate respondsToSelector:@selector(thumbnailControlView:didHoverIndex:)]) {
            [self.delegate thumbnailControlView:self didHoverIndex:1];
        }
    } else if ([buttonType isEqualToString:@"image"]) {
        if ([self.delegate respondsToSelector:@selector(thumbnailControlView:didHoverIndex:)]) {
            [self.delegate thumbnailControlView:self didHoverIndex:0];
        }
    }
}

- (void)mouseExited:(NSEvent *)event {
    // 鼠标离开时可以选择恢复之前的状态
}

- (void)setThumbnailItems:(nonnull NSArray<BannerItem *> *)items {
    // 设置缩略图
    
}

@end
