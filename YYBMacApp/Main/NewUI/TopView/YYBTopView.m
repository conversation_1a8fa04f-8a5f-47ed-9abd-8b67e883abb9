//
//  YYBTopView.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/19.
//

#import "YYBTopView.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "Masonry.h"
#import "YYBComponent.h"
#import "TopViewBannerView.h"
#import <CoreImage/CoreImage.h>
#import "YYBTopViewData.h"
#import "SDWebImage.h"

static NSString *kTag = @"YYBTopView";
@interface YYBTopView ()
@property (nonatomic, strong) YYBComponent *component;
@property (nonatomic, strong) YYBTopViewData *data;
@property (nonatomic, strong) NSImageView *bgImageView;
@end

@implementation YYBTopView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.wantsLayer = YES;
        self.bgImageView = [[NSImageView alloc] initWithFrame:frame];
        self.bgImageView.imageScaling = NSImageScaleAxesIndependently;
        [self addSubview:self.bgImageView];

    }
    return self;
}

- (void)configureWithComponent:(YYBComponent *)component {
    self.component = component;
     if ([self.component.data isKindOfClass:YYBTopViewData.class]) {
        self.data = (YYBTopViewData *)self.component.data;
    }
    // 更新背景图片
    if (self.data.videoImgUrl) {
        [self updateBackground:self.data.videoImgUrl];
    } else {
        [self updateBackground:self.data.snapshotsCoverImages.firstObject];
    }
}

- (void)setEdgeInsets:(NSEdgeInsets)edgeInsets {
    
}

- (void)layout {
    [super layout];
    self.bgImageView.frame = NSMakeRect(0, 0, self.frame.size.width * 0.65, self.frame.size.height);
}

- (void)updateBackground:(NSString *)bgImageUrl {
    if (!bgImageUrl || bgImageUrl.length == 0) {
        return;
    }
    
    [self.bgImageView sd_setImageWithURL:[NSURL URLWithString:bgImageUrl]
                        placeholderImage:nil
                               completed:^(NSImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        if (error) {
            YYBMacLogError(kTag, @"图片加载失败: %@", error.localizedDescription);
            return;
        }
        
        if (!image) {
            return;
        }
        
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            NSImage *blurredImage = [self applyBlurToImage:image];
            dispatch_async(dispatch_get_main_queue(), ^{
                self.bgImageView.image = blurredImage;
            });
        });
    }];
}

- (NSImage *)applyBlurToImage:(NSImage *)inputImage {
    NSData *imageData = [inputImage TIFFRepresentation];
    CIImage *ciImage = [CIImage imageWithData:imageData];
    
    if (!ciImage) {
        return inputImage;
    }

    CIFilter *blurFilter = [CIFilter filterWithName:@"CIGaussianBlur"];
    [blurFilter setValue:ciImage forKey:kCIInputImageKey];
    [blurFilter setValue:@(120.0) forKey:kCIInputRadiusKey]; // 调整模糊半径

    CIImage *outputCIImage = blurFilter.outputImage;
    
    if (!outputCIImage) {
        return inputImage;
    }

    NSCIImageRep *rep = [NSCIImageRep imageRepWithCIImage:outputCIImage];
    NSImage *blurredImage = [[NSImage alloc] initWithSize:rep.size];
    [blurredImage addRepresentation:rep];

    return blurredImage;
}

@end
