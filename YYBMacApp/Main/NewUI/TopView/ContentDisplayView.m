//
//  ContentDisplayView.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/21.
//

#import "ContentDisplayView.h"
#import "Masonry.h"
#import "YYBTPVideo.h"

@interface ContentDisplayView ()
@property (nonatomic, strong) NSImageView *displayImageView;
@property (nonatomic, strong) YYBTPVideo *player;
@property (nonatomic, strong) NSView *playerContainerView;
@end

@implementation ContentDisplayView

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    self.view.wantsLayer = YES;
    self.view.layer.backgroundColor = [NSColor clearColor].CGColor;
    
    // 图片视图
    self.displayImageView = [[NSImageView alloc] init];
    self.displayImageView.imageScaling = NSImageScaleProportionallyUpOrDown;
    self.displayImageView.imageAlignment = NSImageAlignLeft;
    self.displayImageView.wantsLayer = YES;
    [self.view addSubview:self.displayImageView];
    
    // 视频容器
    self.playerContainerView = [[NSView alloc] init];
    self.playerContainerView.wantsLayer = YES;
    self.playerContainerView.layer.backgroundColor = [NSColor blackColor].CGColor;
    [self.view addSubview:self.playerContainerView];
    
    // 约束
    [self setupConstraints];
}

- (void)setupConstraints {
    [self.displayImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(48);
        make.top.equalTo(self).offset(40);
        make.bottom.equalTo(self).offset(-40);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.64);
    }];
    
    [self.playerContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

}

#pragma mark - 内容展示
- (void)displayItem:(nonnull BannerItem *)item {
}

#pragma mark - 布局更新



@end
