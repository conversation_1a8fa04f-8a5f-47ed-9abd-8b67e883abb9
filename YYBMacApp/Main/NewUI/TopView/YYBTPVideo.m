//
//  YYBTPVideo.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/20.
//

#import "Masonry.h"
#import <ThumbPlayer/ITPPlayer.h>
#import <ThumbPlayer/TPMediaAssetFactory.h>
#import <ThumbPlayer/TPPlayerFactory.h>
#import "YYBTPVideo.h"

@interface YYBTPVideo ()<ITPPlayerDelegate>
@property (nonatomic, strong) YYBVideoModel *video;

/// 播放器实例
@property (nonatomic, strong) id<ITPPlayer> player;
/// 播放器渲染view
@property (nonatomic, strong) NSView *playerView;
@property (nonatomic, strong) NSView *superView;

@end

@implementation YYBTPVideo
- (instancetype)initWithVideo:(YYBVideoModel *)video withSuperView:(NSView *)superView {
    self = [super init];

    if (self) {
        self.video = video;
        self.superView = superView;
        _playerView.wantsLayer = YES;
        _playerView.layer = [CALayer layer];
        _playerView.layer.backgroundColor = [NSColor blackColor].CGColor;
        [self initPlayerUI];
        [self setupConstraints];
    }

    return self;
}

- (void)initPlayerUI {
    // 1.创建播放器
    self.player = [TPPlayerFactory createTPPlayer];

    // 2.设置delegate
    [self.player setPlayerDelegate:self];

    // 3.创建播放view并设置给播放器
    self.playerView = [[NSView alloc] initWithFrame:self.superView.bounds];
    [self.superView addSubview:self.playerView];
    self.playerView.autoresizingMask = NSViewWidthSizable | NSViewHeightSizable;

    [self.player setPlayerView:self.playerView];

    // 4.设置播放源
    // url为播放地址，可以是在线url或者本地地址
    id<ITPMediaAsset> asset = [TPMediaAssetFactory createUrlMediaAsset:self.video.url];
    [self.player setDataSource:asset];

    // 5.prepare播放器
    [self.player prepareAsync];
}

- (void)play {
    [self.player start];
}
- (void)pause {
    [self.player pause];
}

- (void)setMute:(BOOL)isMute {
    [self.player setAudioMute:isMute];
}

- (void)setupConstraints {
//    [self.playerView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.edges.equalTo(self.superView);
//    }];
}

- (void)onCompletionWithPlayer:(nonnull id<ITPPlayer>)player {
    [self.player start];
}

- (void)onPlayer:(nonnull id<ITPPlayer>)player error:(nonnull TPError *)error {
}

- (void)onPreparedWithPlayer:(nonnull id<ITPPlayer>)player {
    [self.player start];
}

@end
