//
//  YYBBannerItem.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/21.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, YYBBannerContentType) {
    Y<PERSON>BBannerContentTypeImage,
    YYBBannerContentTypeVideo,
    YYBBannerContentTypeActive, // 活动
};

@interface YYBBannerItem : NSObject
@property (nonatomic, strong) NSString *contentPath;
@property (nonatomic, assign) YYBBannerContentType contentType;
@property (nonatomic, strong, nullable) NSString *thumbnailUrl;
@property (nonatomic, assign) NSTimeInterval duration; // 视频时长或图片展示时长
@end

NS_ASSUME_NONNULL_END
