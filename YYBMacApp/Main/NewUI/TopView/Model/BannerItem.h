//
//  BannerItem.h
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/21.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, BannerItemType) {
    BannerItemTypeImage,
    BannerItemTypeVideo,
    BannerItemTypeActivity,

};

@interface BannerItem : NSObject

@property (nonatomic, assign) BannerItemType type;
@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) NSString *resourcePath; // 图片路径或视频路径
@property (nonatomic, strong) NSArray<NSString *> *imagePaths; // 图集路径数组
@property (nonatomic, strong) NSString *thumbnailPath; // 缩略图路径
@property (nonatomic, assign) NSInteger currentImageIndex; // 当前显示的图片索引（仅图集）

- (instancetype)initWithType:(BannerItemType)type
                       title:(NSString *)title
                resourcePath:(NSString *)resourcePath
                 imagePaths:(nullable NSArray<NSString *> *)imagePaths
               thumbnailPath:(NSString *)thumbnailPath;

@end

NS_ASSUME_NONNULL_END
