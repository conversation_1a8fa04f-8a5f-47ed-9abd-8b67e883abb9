//
//  MainUIDefine.h
//  YYBMacApp
//
//  Created by halehuang on 2025/8/20.
//

/**
 * 主窗口UI定义
 */
#ifndef MainUIDefine_h
#define MainUIDefine_h

#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>

// 左侧导航栏宽度
static CGFloat const kNavigationDefaultWidth = 80;
static CGFloat const kNavigationExpandWidth = 144;
// 导航栏图标大小
static CGFloat const kNavigationIconSize = 44.0f;
// 顶部工具栏高度
static CGFloat const kTopBarHeight = 88;
// 导航栏展开动画
static CGFloat const kExpandAnimationDuration = 0.2f;
#define kExpandAnimationTimingFunction kCAMediaTimingFunctionEaseInEaseOut
// 导航栏展开状态改变通知
FOUNDATION_EXPORT NSString * const kYYBMainNavigationViewExpandedDidChangeNotification;

#endif /* MainUIDefine_h */
