//
//  NSImage+Utils.h
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/22.
//

#import <Cocoa/Cocoa.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSImage (Utils)

/// 返回 inputImage 按指定 NSColor 重绘后的新图片（仅适合原图为模板图或纯色区域）
+ (NSImage *)imageWithTint:(NSImage *)inputImage color:(NSColor *)color;

/// 生成旋转后新图片，顺时针角度（degree, 可为任意值，正负均可，0/360等价不旋转）
+ (NSImage *)rotatedImage:(NSImage *)img byDegrees:(CGFloat)degrees;

@end

NS_ASSUME_NONNULL_END
