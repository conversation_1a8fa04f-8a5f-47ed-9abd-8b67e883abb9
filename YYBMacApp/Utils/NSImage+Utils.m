//
//  NSImage+Utils.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/22.
//

#import "NSImage+Utils.h"

@implementation NSImage (Utils)

+ (NSImage *)imageWithTint:(NSImage *)inputImage color:(NSColor *)color {
    if (!inputImage || !color) return nil;
    // 拷贝，防止修改原图
    NSImage *image = [inputImage copy];
    [image lockFocus];
    [color set];
    NSRect imageRect = {NSZeroPoint, image.size};
    NSRectFillUsingOperation(imageRect, NSCompositingOperationSourceAtop);
    [image unlockFocus];
    return image;
}

+ (NSImage *)rotatedImage:(NSImage *)img byDegrees:(CGFloat)degrees {
    if (!img) return nil;
    NSSize imgSize = img.size;
    NSImage *rotatedImage = [[NSImage alloc] initWithSize:imgSize];

    [rotatedImage lockFocus];

    NSGraphicsContext* context = [NSGraphicsContext currentContext];
    [context saveGraphicsState];

    // 坐标变换：以图片中心旋转
    NSAffineTransform* transform = [NSAffineTransform transform];
    // 先平移至中心，旋转，再平移回起始点
    [transform translateXBy:(imgSize.width/2) yBy:(imgSize.height/2)];
    [transform rotateByDegrees:degrees];
    [transform translateXBy:-(imgSize.width/2) yBy:-(imgSize.height/2)];
    [transform concat];

    // 绘制图片
    [img drawAtPoint:NSZeroPoint fromRect:NSMakeRect(0, 0, imgSize.width, imgSize.height) operation:NSCompositingOperationSourceOver fraction:1.0];

    [context restoreGraphicsState];
    [rotatedImage unlockFocus];

    return rotatedImage;
}

@end
