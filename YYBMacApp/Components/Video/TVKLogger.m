//
//  TVKLogger.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/20.
//

#import <YYBMacFusionSDK/YYBMacLog.h>
#import "TVKLogger.h"

@implementation TVKLogger

- (void)logWithLevel:(TVKLogLevel)logLevel
                 tag:(NSString *)tag
                file:(const char *)file
            function:(const char *)function
                line:(NSUInteger)line
              format:(NSString *)format
                args:(va_list)args {
    YYBMacLogLevel yybLevel = YYBMacLogLevelVerbose;

    switch (logLevel) {
        case TVKLogLevelVerbose:
            yybLevel = YYBMacLogLevelVerbose;
            break;

        case TVKLogLevelDebug:
            yybLevel = YYBMacLogLevelDebug;
            break;

        case TVKLogLevelInfo:
            yybLevel = YYBMacLogLevelInfo;
            break;

        case TVKLogLevelSystem:
            yybLevel = YYBMacLogLevelInfo;
            break;

        case TVKLogLevelWarning:
            yybLevel = YYBMacLogLevelWarn;
            break;

        case TVKLogLevelError:
            yybLevel = YYBMacLogLevelError;
            break;

        default:
            break;
    }
    [[YYBMacLog sharedInstance] log:yybLevel tag:tag file:file func:function line:(int)line format:format, args];
}

@end

@implementation TPLogger
- (void)logWithLevel:(TPLogLevel)logLevel tag:(NSString *)tag file:(NSString *)file function:(NSString *)function line:(NSUInteger)line content:(NSString *)content {
    YYBMacLogLevel yybLevel = YYBMacLogLevelVerbose;

    switch (logLevel) {
        case TPLogLevelVerbose:
            yybLevel = YYBMacLogLevelVerbose;
            break;

        case TPLogLevelDebug:
            yybLevel = YYBMacLogLevelDebug;
            break;

        case TPLogLevelInfo:
            yybLevel = YYBMacLogLevelInfo;
            break;

        case TPLogLevelWarning:
            yybLevel = YYBMacLogLevelWarn;
            break;

        case TPLogLevelError:
            yybLevel = YYBMacLogLevelError;
            break;

        default:
            break;
    }
    [[YYBMacLog sharedInstance] log:(YYBMacLogLevel)logLevel tag:tag file:file.UTF8String func:function.UTF8String line:(int)line format:@"%@", content];
}

@end
