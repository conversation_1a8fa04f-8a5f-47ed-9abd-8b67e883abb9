//
//  YYBVideoManager.m
//  YYBMacApp
//
//  Created by <PERSON> on 2025/8/20.
//

#import "YYBVideoManager.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import <TVKPlayerForMac/TVKSDK.h>
#import "TVKLogger.h"

#import <ThumbPlayer/TPMgr.h>
#import <ThumbPlayer/TPMgrConfig.h>

const NSInteger kPlatform = 8460903;

@interface YYBVideoManager ()
@end

@implementation YYBVideoManager
+ (instancetype)sharedInstance {
    static YYBVideoManager *s_instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        s_instance = [[YYBVideoManager alloc] init];
    });

    return s_instance;
}

- (void)initSDK {
#if TVK_USE
    [self initTVKSDK];
#else
    [self initTPSDK];
#endif
}

// SDK配置
- (void)initTVKSDK {
    //注册SDK（必须设置，经过鉴权后，从register解析出播放的信息才能正式播放腾讯视频的源
    [TVKSDK setLogDelegate:[[TVKLogger alloc] init]];                                     //设置播放器的日志代理
    //TODO: 还在申请
    BOOL regSuc = [TVKSDK registerWithAppKey:@"" guid:nil];  //设置从接口人获取的appkey.注册不成功，则无法进行播放
    if (regSuc) {
        NSLog(@"初始化VideoSDK成功");
    } else {
        NSLog(@"初始化VideoSDK失败,请检查appkey和bundleid是否匹配");
    }
}

- (void)initTPSDK {
    // 设置日志打印delegate。ThumbPlayer本身没有日志打印模块，若要输出或保存日
    // 志，须设置该delegate，播放器通过该delegate将日志回调到应用层
    [[TPMgr sharedMgr] setLogDelegate:[[TPLogger alloc] init]];

    // 设置日志输出级别
    [[TPMgr sharedMgr] setLogLevel:TPLogLevelInfo];

    // 一些config在init之前设置才能生效，比如平台号等。
    // 请参考TPMgrConfig中key的定义
    TPOptionalParam *optionalParam = [TPOptionalParam buildIntParam:(int)kPlatform key:TPMgrConfigKeyBeforeIntPlatform];
    [[TPMgr sharedMgr] addOptionalParam:optionalParam];

    // 初始化ThumbPlayer。开始播放前，必须调用该初始化接口
    [[TPMgr sharedMgr] initThumbPlayer];
}
@end
