//
//  YYBDownloadButton.m
//  YYBMacApp
//
//  Created by 凌刚 on 2025/8/18.
//

#import "YYBDownloadButton.h"
#import "Masonry.h"
#import "YYBDownloadMainButton.h"
#import "YYBDownloadStatusIcon.h"
#import "YYBApkInfoViewModelPool.h"
#import "YYBApkInfoViewModel.h"
#import "YYBApkInfoModel.h"
#import "YYBAria2DownloadManager.h"
#import "YYBApkPackage.h"
#import <YYBMacFusionSDK/YYBMacLog.h>
#import "YYBLibAria2ServiceFacade.h"
#import "YYBDirectory.h"

static NSString *const kTag = @"YYBDownloadButton";

@interface YYBDownloadButton () <YYBApkInfoViewModelDelegate>

@property (nonatomic, strong) YYBDownloadMainButton *mainButton;
@property (nonatomic, strong) YYBDownloadStatusIcon *statusIcon;

@property (nonatomic, strong, readwrite) YYBApkInfoViewModel *viewModel;
@property (nonatomic, strong, readonly) YYBApkInfoModel *model;

// 自动启动（下载完后是否自动安装，安装完后是否自动打开, 需要用户有明确操作后才开启）
@property (nonatomic, assign, readonly) BOOL autoStart;
// 当前样式状态（便于外部自适应）
@property (nonatomic, assign) NSSize buttonSize;

// 是否处于鼠标悬停（用于进度模式下展示“暂停”文案）
@property (nonatomic, assign) BOOL isHovering;

@end

@implementation YYBDownloadButton

- (instancetype)initWithFrame:(NSRect)frameRect {
    if (self = [super initWithFrame:frameRect]) {
        _buttonSize = frameRect.size; // 默认按钮大小
        [self buildSubviews];
        [self buildConstraints];
        [self setupMouseTracking];
        YYBMacLogInfo(kTag, @"[initWithFrame] YYBDownloadButton size=(%.0f, %.0f)", _buttonSize.width, _buttonSize.height);
    }
    return self;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        _buttonSize = CGSizeMake(320, 48); // 默认按钮大小
        [self buildSubviews];
        [self buildConstraints];
        [self setupMouseTracking];
        YYBMacLogInfo(kTag, @"[init] YYBDownloadButton size=(%.0f, %.0f)", _buttonSize.width, _buttonSize.height);
    }
    return self;
}

- (void)dealloc {
    [self.viewModel removeDelegate:self];
    YYBMacLogInfo(kTag, @"[dealloc] YYBDownloadButton");
}

#pragma mark - Size/布局变化（外部驱动）

- (void)updateButtonSize:(NSSize)size {
    // 限定合合理边界
    if (size.width < 70) size.width = 70;
    if (size.height < 32) size.height = 32;
    _buttonSize = size;
    [self mas_updateConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(size);
    }];
    // 刷新全部子控件布局
    self.layer.cornerRadius = self.buttonSize.height / 2;
    [self setNeedsLayout:YES];
    [self layoutSubtreeIfNeeded];
    [self buildConstraints];
    YYBMacLogInfo(kTag, @"[setButtonSize] update to (%.0f, %.0f)", size.width, size.height);
}

- (void)resizeSubviewsWithOldSize:(NSSize)oldSize {
    [super resizeSubviewsWithOldSize:oldSize];
    [self buildConstraints];
}

#pragma mark - 绑定VM
- (void)setApkInfoModel:(YYBApkInfoModel *)model {
    if (!model) {
        YYBMacLogInfo(kTag, @"[setApkInfoModel] model is nil, ignore");
        return;
    }
    if (self.viewModel) {
        [self.viewModel removeDelegate:self];
    }
    self.viewModel = [[YYBApkInfoViewModelPool sharedPool] viewModelForModel:model];
    [self.viewModel addDelegate:self];
    YYBMacLogInfo(kTag, @"[setApkInfoModel bind] pkg=%@", model.pkgName);
    [self refreshUIWithState:self.model.apkState progress:self.model.downloadPercent error:nil];
}

- (void)setPkgName:(NSString *)pkgName {
    if (!pkgName) {
        YYBMacLogInfo(kTag, @"[setPkgName] pkgName is nil, ignore");
        return;
    }
    if (self.viewModel) {
        [self.viewModel removeDelegate:self];
    }
    self.viewModel = [[YYBApkInfoViewModelPool sharedPool] viewModelForPkgName:pkgName];
    [self.viewModel addDelegate:self];
    YYBMacLogInfo(kTag, @"[setPkgName bind] pkg=%@", pkgName);
    [self refreshUIWithState:self.model.apkState progress:self.model.downloadPercent error:nil];
}

- (YYBApkInfoModel *)model {
    return self.viewModel.model;
}

- (BOOL)autoStart {
    return self.viewModel.autoStart;
}

#pragma mark - UI 构建
- (void)buildSubviews {
    self.wantsLayer = YES;
    self.layer.cornerRadius = self.buttonSize.height / 2;
    self.layer.masksToBounds = YES;
    self.layer.backgroundColor = [NSColor clearColor].CGColor;
    
    // 主按钮
    _mainButton = [[YYBDownloadMainButton alloc] initWithFrame:NSZeroRect];
    _mainButton.target = self;
    _mainButton.action = @selector(onMainButtonClicked);
    [self addSubview:_mainButton];
    
    // 状态icon
    _statusIcon = [[YYBDownloadStatusIcon alloc] initWithFrame:NSZeroRect];
    [self addSubview:_statusIcon];
}

// Masonry布局：自适应buttonSize和内容，圆角等与父view一致
- (void)buildConstraints {
    [self.mainButton mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    [self.statusIcon mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.mainButton);
        make.width.height.mas_equalTo(_buttonSize.height * 0.6);
    }];
}

#pragma mark - 鼠标事件（进度态 hover 显示“暂停”）

- (void)setupMouseTracking {
    NSTrackingArea *area = [[NSTrackingArea alloc] initWithRect:self.bounds
                                                        options:NSTrackingMouseEnteredAndExited | NSTrackingActiveInKeyWindow | NSTrackingMouseMoved
                                                          owner:self
                                                       userInfo:nil];
    [self addTrackingArea:area];
}

- (void)mouseEntered:(NSEvent *)theEvent {
    [super mouseEntered:theEvent];
    if ([self.model.apkState isEqualToString:YYBApkStateActive]) {
        self.isHovering = YES;
        [self refreshUIWithState:self.model.apkState progress:self.model.downloadPercent error:nil];
        YYBMacLogInfo(kTag, @"[mouseEntered] show pause on progress pkg=%@", self.model.pkgName);
    }
}

- (void)mouseExited:(NSEvent *)theEvent {
    [super mouseExited:theEvent];
    self.isHovering = NO;
    [self refreshUIWithState:self.model.apkState progress:self.model.downloadPercent error:nil];
    YYBMacLogInfo(kTag, @"[mouseExited] show percent on progress pkg=%@", self.model.pkgName);
}

#pragma mark - VM Delegate（主线程回调）
- (void)apkInfoViewModel:(YYBApkInfoViewModel *)viewModel didUpdateModel:(YYBApkInfoModel *)model error:(NSError *)error {
    [self refreshUIWithState:model.apkState progress:model.downloadPercent error:error];
    
    NSString *stateString = [self.model apkStateLogText];
    NSString *pkgName = self.model.pkgName;
    if (model.apkState == YYBApkStateComplete && viewModel.autoStart) {
        YYBMacLogInfo(kTag, @"[VM Delegate 自动] 开始安装--apk,  pkg=%@ state=%@", pkgName, stateString);
        [self installApk];
    } else if (model.apkState == YYBApkStateInstalled && viewModel.autoStart) {
        YYBMacLogInfo(kTag, @"[VM Delegate 自动] 开始打开--apk,  pkg=%@ state=%@", pkgName, stateString);
        [self openApk];
    } else if (model.apkState == YYBApkStateFetchComplete && viewModel.autoStart) {
        YYBMacLogInfo(kTag, @"[VM Delegate 自动] 开始下载--apk,  pkg=%@ state=%@", pkgName, stateString);
        [self downloadApk];
    }
}

#pragma mark - UI 刷新（状态 → 文案/样式 映射）
- (void)refreshUIWithState:(NSString *)apkState progress:(double)progress error:(NSError * _Nullable)error {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self refreshUIWithState:apkState progress:progress error:error];
        });
        return;
    }
    // 分发状态到子控件，由子控件自己解析状态渲染内容
    [self.mainButton setApkState:apkState
                       autoStart:self.autoStart
                       haveCache:self.viewModel.haveCache
                        progress:progress
                           error:error
                      isHovering:self.isHovering];
    [self.statusIcon setApkState:apkState autoStart:self.autoStart error:error loadingSize:self.buttonSize.height];
    YYBMacLogInfo(kTag, @"[refreshUI] pkg=%@ state=%@ progress=%.2f%% size=(%.0f,%.0f)", self.model.pkgName, apkState, progress*100, _buttonSize.width, _buttonSize.height);
}

#pragma mark - 下载apk存放目录
- (NSString *)downloadApkPath {
    NSString *sharedDownloadDir = [[YYBLibAria2ServiceFacade sharedService] sharedDownloadDir];
    NSString *apkPath = [sharedDownloadDir stringByAppendingPathComponent:[[YYBLibAria2ServiceFacade sharedService] apkDirName]];
    // 每次都确保目录存在
    return [YYBDirectory ensureDirectoryExists:apkPath];
}

#pragma mark - 点击动作
- (void)onMainButtonClicked {
    NSString *state = self.model.apkState ?: YYBApkStateINIT;
    NSString *stateString = [self.model apkStateLogText];
    NSString *pkgName = self.model.pkgName;
    YYBMacLogInfo(kTag, @"[onMainButtonClicked] pkg=%@ state=%@", pkgName, stateString);
    
    // 已安装 → 打开
    if ([state isEqualToString:YYBApkStateInstalled] ||
        [state isEqualToString:YYBApkStateCompleteLaunch] ||
        [state isEqualToString:YYBApkStateInstalling]) {
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] 打开--apk,  pkg=%@ state=%@", pkgName, stateString);
        [self openApk];
        return;
    }
    
    // 拉取apkinfo信息
    if (!self.viewModel.model.apkUrl.length) {
        self.viewModel.autoStart = YES;
        if ([self.viewModel fetchFullAppInfoIfNeeded]) {
            YYBMacLogInfo(kTag, @"[onMainButtonClicked] in fetchFullAppInfoIfNeeded,  pkg=%@ state=%@", pkgName, stateString);
            return;
        }
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] no apkurl, not in fetchFullAppInfoIfNeeded,  pkg=%@ state=%@", pkgName, stateString);
        return;
    }

    // 下载中 → 暂停
    if ([state isEqualToString:YYBApkStateActive]) {
        YYBAria2Task *task = self.viewModel.downloadApkInfo;
        if (task) {
            YYBMacLogInfo(kTag, @"[onMainButtonClicked] 暂停--apk,  pkg=%@ state=%@", pkgName, stateString);
            [self pausApk:task];
        }
        return;
    }
    
    // 暂停 → 恢复
    if ([state isEqualToString:YYBApkStatePaused]) {
        YYBAria2Task *task = self.viewModel.downloadApkInfo;
        if (task) {
            YYBMacLogInfo(kTag, @"[onMainButtonClicked] 恢复下载1--apk,  pkg=%@ state=%@", pkgName, stateString);
            self.viewModel.autoStart = YES;
            [self continueApk:task];
        }
        return;
    }
    
    //  开始或重试下载
    if ([state isEqualToString:YYBApkStateINIT] ||
        [state isEqualToString:YYBApkStateRemoved] ||
        [state isEqualToString:YYBApkStateEndUninstall] ||
        [state isEqualToString:YYBApkStateError] ||
        [state isEqualToString:YYBApkStateInstallError] ||
        [state isEqualToString:YYBApkStateFetchComplete]) {
        
        // 如果已有任务且可恢复，优先恢复
        YYBAria2Task *task = self.viewModel.downloadApkInfo;
        if (task && [task canUnpause]) {
            YYBMacLogInfo(kTag, @"[onMainButtonClicked] 恢复下载2--apk,  pkg=%@ state=%@", pkgName, stateString);
            self.viewModel.autoStart = YES;
            [self continueApk:task];
            return;
        }
        
        // 新建任务（需要URL）
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] 开始下载--apk,  pkg=%@ state=%@", pkgName, stateString);
        self.viewModel.autoStart = YES;
        [self downloadApk];
        return;
    }
    
    // 下载完成 -> 安装
    if ([state isEqualToString:YYBApkStateComplete]) {
        // 尝试触发安装
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] 开始安装--apk,  pkg=%@ state=%@", pkgName, stateString);
        self.viewModel.autoStart = YES;
        [self installApk];
        return;
    }
    
    // NeedUpdate -> 触发“更新”（通常也是下载+安装）, 更新不自动打开
    if ([state isEqualToString:YYBApkStateNeedUpdate]) {
        // 与 INIT 类似，走下载流程
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] 更新（触发下载+安装）--apk,  pkg=%@ state=%@", pkgName, stateString);
        [self updateApk];
        return;
    }
    
    // 加载中态/不可操作态
    YYBMacLogInfo(kTag, @"[onMainButtonClicked] 不可点击态 - 无响应,  pkg=%@ state=%@", pkgName, stateString);
}

- (void)openApk {
    [[YYBApkPackage shared] openApp:self.model.pkgName];
}

- (void)pausApk:(YYBAria2Task *)task {
    [[YYBAria2DownloadManager sharedManager] pauseTask:task completion:^(BOOL success, NSError * _Nullable error) {
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] pause result=%d error=%@", success, error);
    }];
}

- (void)continueApk:(YYBAria2Task *)task {
    [[YYBAria2DownloadManager sharedManager] resumeTask:task completion:^(BOOL success, NSError * _Nullable error) {
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] resume(recoverable) result=%d error=%@", success, error);
    }];
}

- (void)downloadApk {
    NSString *url = self.model.apkUrl;
    if (!url.length) {
        YYBMacLogError(kTag, @"[onMainButtonClicked] no downloadURL provided,  pkg=%@", self.model.pkgName);
        return;
    }
    
    YYBAria2Task *newTask = [[YYBAria2DownloadManager sharedManager] createDownloadTaskWithURL:url
                                                                                       destDir:[self downloadApkPath]
                                                                                      fileName:nil
                                                                                           md5:self.model.md5
                                                                                    visibility:YYBAria2TaskVisibilityUser
                                                                                      priority:YYBAria2TaskPriorityNormal];
    newTask.pkgName = self.model.pkgName;
    newTask.apkName = self.model.name;
    newTask.apkIcon = self.model.icon;
    newTask.source = YYBAria2TaskSourceDownloadButton;
    [[YYBAria2DownloadManager sharedManager] startTask:newTask];
    YYBMacLogInfo(kTag, @"[onMainButtonClicked] create & start new task, pkg=%@ url=%@", self.model.pkgName, url);
}

- (void)installApk {
    InstallApkInfo *installApkInfo = [InstallApkInfo createInstallApkInfoFromApkInfo:self.model
                                                                     downloadApkInfo:self.viewModel.downloadApkInfo];
    [[YYBApkPackage shared] installApp:installApkInfo completion:^(InstallApkInfo * _Nullable apkInfo, NSInteger retCode, NSString * _Nullable errorMessage) {
        YYBMacLogInfo(kTag, @"[onMainButtonClicked] install code=%ld msg=%@", (long)retCode, errorMessage);
    }];
}

- (void)updateApk {
    [self downloadApk];
}

@end
