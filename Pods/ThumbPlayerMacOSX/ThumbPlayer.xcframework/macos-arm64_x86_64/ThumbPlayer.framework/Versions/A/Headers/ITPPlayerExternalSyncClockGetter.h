/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     ITPPlayerExternalSyncClockGetter.h
 * @brief    播放同步时钟代理
 * <AUTHOR>
 * @version  1.0.0
 * @date     2025/1/24
 * @license  GNU General Public License (GPL)
 *****************************************************************************/
#import <Foundation/Foundation.h>
#import "ITPPlayerSyncSource.h"
#import "TPPlayerSyncClock.h"

/**
 * 播放器外部时钟获取器
 *
 * <AUTHOR>
 * @date 2025-1-19
 */
@protocol ITPPlayerExternalSyncClockGetter <ITPPlayerSyncSource>

/**
 * 获取外部时钟
 * @return 外部时钟
 */
- (TPPlayerSyncClock *)getExternalSyncClock;

@end
